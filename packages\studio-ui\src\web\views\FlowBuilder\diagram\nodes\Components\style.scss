.secondaryText {
  color: #5c7080;
  display: block;
}

.text {
  ul {
    padding-left: 20px;
    margin: 0;
  }
}

.contentImgWrapper {
  display: flex;
  max-height: 50px;

  .textWrapper {
    text-align: left;
    overflow: hidden;
  }
}

.img {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 3px;
  flex-shrink: 0;
  height: 30px;
  margin-right: var(--spacing-medium);
  overflow: hidden;
  width: 30px;
}

:global(.srd-node):active {
  cursor: grabbing;
}

.large {
  width: 200px !important;

  .contentWrapper {
    width: 187px;
  }
}

.nodeWrapper {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid transparent !important;
  width: 150px;
  border: 1px solid transparent;
  border-radius: 0 0 5px 5px;
  transition: border 0.3s;

  &.highlighted {
    border: 1px solid var(--ocean) !important;

    .standard,
    .skill-call,
    .action,
    .failure,
    .success {
      :global(.port):before {
        background: var(--ocean);
      }
    }

    .outRouting:before {
      background-color: var(--ocean) !important;
    }

    .debugInfo {
      background: var(--ocean) !important;
      color: #fff;
    }

    .hasError {
      border: 1px solid var(--lighthouse);
      background: var(--lighthouse) !important;
    }

    .headerWrapper {
      &.sub-workflow,
      &:hover.sub-workflow,
      &.action,
      &:hover.action,
      &.failure,
      &:hover.failure,
      &.success,
      &:hover.success,
      &.standard,
      &:hover.standard,
      &.skill-call,
      &:hover.skill-call {
        border-color: var(--ocean);
      }
    }
  }

  .action :global(.port):before {
    background: var(--execute);
  }

  .failure :global(.port):before {
    background: var(--lighthouse);
  }

  .success :global(.port):before {
    background: var(--success);
  }

  .standard :global(.port):before {
    background: var(--standard);
  }

  .skill-call :global(.port):before {
    background: var(--skill-call);
  }

  :global {
    .port {
      background: none !important;
      height: 40px;
      width: 25px;

      &:before {
        content: '';
        border-radius: 100%;
        height: 6px;
        width: 6px;
        background: var(--say);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      &.selected {
        &:before {
          background: var(--say);
        }
      }
    }
  }

  .contentsWrapper {
    padding: 0 var(--spacing-medium) var(--spacing-medium);
    line-height: 1;

    :global {
      .content-wrapper {
        margin-right: 0 !important;

        &:first-of-type {
          margin-top: 0 !important;
        }

        &:hover {
          &:after {
            background: #fff !important;
          }
        }
      }
    }
  }

  .debugInfo {
    padding: 3px;
    font-size: 10px;
    text-align: center;
    font-weight: normal;

    div {
      min-height: 15px;
      padding: 4px;
    }

    &.action,
    &:hover.action {
      background: var(--execute);
    }

    &.failure,
    &:hover.failure {
      background: var(--lighthouse);
    }

    &.success,
    &:hover.success {
      background: var(--success);
    }

    &.standard,
    &:hover.standard {
      background: var(--standard);
    }

    &.skill-call,
    &:hover.skill-call {
      background: var(--skill-call);
    }

    :global(.bp3-button).button {
      background-color: var(--seashell);
      color: var(--shark);
      min-height: 0;
      border-radius: 3px;
      font-size: 10px;
      height: 20px;
      padding: 6px;
      transition: background 0.3s ease-in-out;

      &:hover {
        background-color: var(--gray);
      }

      &.danger {
        background-color: var(--lighthouse);
        color: white;

        &:hover {
          background-color: var(--hover-lighthouse);
        }
      }
    }
  }

  .headerWrapper {
    background: #fff;
    border: none;
    border-top: 2px solid var(--say);
    box-shadow: none;
    border-radius: 0;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    color: var(--shark);
    font-size: 12px;
    font-weight: bold;
    justify-content: flex-start;
    line-height: 1.08;
    margin: 0;
    min-height: 32px;
    position: relative;
    width: 100%;

    &.action,
    &:hover.action {
      border-color: var(--execute);
    }

    &.failure,
    &:hover.failure {
      border-color: var(--lighthouse);
    }

    &.success,
    &:hover.success {
      border-color: var(--success);
    }

    &.standard,
    &:hover.standard {
      border-color: var(--standard);
    }

    &.skill-call,
    &:hover.skill-call {
      border-color: var(--skill-call);
    }

    .button {
      background: none !important;
      border: none !important;
      box-shadow: none !important;
      color: var(--shark);
      display: flex;
      font-size: 12px;
      font-weight: bold;
      justify-content: flex-start;
      line-height: 1.08;
      margin: 0 !important;
      min-height: 30px;
      padding: 0 var(--spacing-medium) !important;
      width: 100%;
      align-items: center;
      position: relative;

      &:active {
        cursor: grabbing;
      }

      svg {
        fill: var(--shark) !important;
      }

      input {
        background-color: #fff;
        border-radius: 5px;
        border: solid 1px var(--ocean);
        color: var(--shark);
        font-size: 12px;
        line-height: 1.08;
        margin-top: -2px;
        padding: 4px 6px 3px;
        width: 100%;

        &::placeholder {
          opacity: 1;
          color: var(--reef);
        }

        &.error {
          border-color: var(--lighthouse) !important;
        }
      }

      :global(.bp3-button-text) {
        padding: var(--spacing-medium) 0;
        word-break: break-word;
      }

      .errorIcon {
        cursor: default;
        position: absolute;
        top: 50%;
        right: 3px;
        line-height: 0;
        transform: translate(-100%, -50%);
        z-index: 99999;

        svg {
          fill: var(--lighthouse) !important;
        }
      }
    }
  }

  .in {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translate(-50%, -50%);
  }
  .out {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
  }
  .outRouting {
    position: absolute;
    top: 50%;
    right: calc(var(--spacing-small) * -1);
    transform: translate(55%, -50%);
  }
}

.joinLabel {
  color: var(--shark);
  display: block;
  font-size: 10px;
  font-weight: bold;
  line-height: 1.25;
  margin: var(--spacing-small) 0;
  text-align: center;

  &:last-of-type {
    display: none;
  }
}

.contentWrapper {
  background: #fff;
  border-radius: 5px;
  border: none;
  color: var(--shark);
  font-size: 10px;
  height: 40px;
  line-height: 1.25;
  margin: 0 0 var(--spacing-small) 0;
  padding: 0;
  position: relative;
  width: 135px;

  &.small {
    height: auto;
    min-height: 20px;
  }

  &:first-of-type {
    margin-top: 0;
  }

  &.hasJoinLabel {
    margin-top: 0;
  }

  &:active {
    cursor: grabbing;
  }

  &.active {
    .content {
      background-color: var(--hover-ocean);
      border: solid 1px var(--ocean);
    }
  }
  &:hover {
    .content:not(.promptPortContent):not(.readOnly) {
      border: solid 1px var(--ocean);
    }
  }
}

:global(.portContainer).outRouting {
  background: none !important;
  right: 0;
  transform: none;
  top: 0;
  position: static;

  :global(.label) {
    position: absolute;
    top: 50%;
    right: calc(var(--spacing-large) * -1);
    transform: translate(100%, -50%);
  }
}

.content {
  align-items: center;
  background: #fff;
  border-radius: 5px;
  border: 1px solid var(--gray);
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  padding: var(--spacing-small);
  text-align: left;
  transition: background 0.3s, border 0.3s;
  width: 100%;

  &.readOnly {
    border-color: var(--bg);
    background-color: var(--bg);
    position: relative;
  }
}

.results {
  font-size: 10px;
}

.total {
  font-size: 14px;
  text-align: center;
}

.smallButton {
  background-color: var(--seashell) !important;
  color: var(--shark);
  min-height: 0;
  border-radius: 3px;
  font-size: 10px;
  height: 20px;
  padding: 6px;
  transition: background 0.3s ease-in-out;

  &:hover {
    background-color: var(--gray);
  }
}

.hidden {
  display: none;
}

.rtl {
  direction: rtl;
}
