name: Purge Cache
on:
  workflow_dispatch: {}
jobs:
  purge-cache:
    runs-on: ubuntu-latest
    steps:
      - name: Delete Cache Entries
        run: |
          gh extension install actions/gh-actions-cache

          REPO=${{ github.repository }}
          BRANCH=${{ github.ref_name }}

          echo "Fetching list of cache keys for branch '$BRANCH'"
          cacheKeysForBranch=$(gh actions-cache list --limit 100 -R $REPO -B $BRANCH | cut -f 1 )

          ## Setting this to not fail the workflow while deleting cache keys. 
          set +e
          echo "Deleting cache entries..."

          for cacheKey in $cacheKeysForBranch
          do
              echo "Deleting entry: $cacheKey"
              gh actions-cache delete $cacheKey -R $REPO -B $BRANCH --confirm
          done

          echo "Done"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
