{"name": "@botpress/ui-shared", "version": "0.0.1", "private": true, "publisher": "botpress", "main": "dist/index.js", "scripts": {"watch": "node ./webpack.config.js --watch", "build": "cross-env NODE_ENV=production node ./webpack.config.js --compile --nomap && yarn scss", "scss": "node-sass src/theme/main.scss dist/theme.css --importer=../../node_modules/node-sass-tilde-importer", "clean": "rimraf dist node_modules/.cache"}, "devDependencies": {"@blueprintjs/core": "^3.23.1", "@blueprintjs/datetime": "3.15.2", "@blueprintjs/select": "^3.12.0", "@types/react": "^16.8.19", "@types/react-dom": "^16.8.4", "cross-env": "^7.0.3", "css-loader": "^0.28.11", "css-modules-typescript-loader": "^2.0.3", "filemanager-webpack-plugin": "3.1.1", "hard-source-webpack-plugin": "^0.13.1", "node-sass": "^7.0.0", "node-sass-tilde-importer": "^1.0.2", "postcss-loader": "^2.0.8", "prop-types": "^15.6.0", "react": "^16.8.6", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^16.8.6", "react-dotdotdot": "^1.3.1", "react-hotkeys": "^1.1.4", "react-intl": "^3.12.1", "react-router-dom": "4.3.1", "react-split-pane": "^0.1.89", "rimraf": "^3.0.2", "sass-loader": "^6.0.6", "script-loader": "^0.7.0", "snarkdown": "^1.2.2", "style-loader": "^0.13.1", "terser-webpack-plugin": "^2.2.2", "ts-loader": "^6.0.2", "typescript": "^4.5.5", "webpack": "4.46", "webpack-bundle-analyzer": "^3.5.2", "webpack-node-externals": "^1.7.2"}, "files": ["dist"], "dependencies": {"@botpress/studio-be": "*", "@cospired/i18n-iso-languages": "^4.0.0", "axios": "^0.25.0", "js-cookie": "^2.2.1", "moment": "^2.29.1", "react-command-palette": "^0.12.0-0", "react-router": "4.3.1"}}