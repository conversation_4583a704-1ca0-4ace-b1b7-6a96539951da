name: Publish Packages

on:
  workflow_dispatch: {}
  push:
    branches:
      - master

permissions:
  id-token: write
  contents: read

jobs:
  publish-packages:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    env:
      NODE_OPTIONS: '--max_old_space_size=8192'
    steps:
      - uses: actions/checkout@v2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          extra_filters: '-F @botpress/*'

      - name: Publish Client
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/client'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish SDK
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/sdk'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish CLI
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/cli'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish Chat Client
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/chat-client'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish Cognitive Client
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/cognitive'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish Vai
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/vai'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish Zai
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/zai'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
      - name: Publish LLMz
        uses: botpress/gh-actions/publish-if-not-exists@master
        with:
          path: './packages/llmz'
          token: '${{ secrets.NPM_ACCESS_TOKEN }}'
