// Copyright 2016 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "~@blueprintjs/icons/src/icons";
@import "../../common/mixins";
@import "../button/common";

@mixin slider-orientation($size, $vertical: false) {
  $slider-min-size: $pt-grid-size * 15;

  @if $vertical == false {
    width: 100%;
    min-width: $slider-min-size;
    height: $size;
  } @else {
    // define the same rules for both vertical as for horizontal to ensure all
    // horizontal rules are fully overridden.
    width: $size;
    min-width: $size;
    height: $slider-min-size;
  }
}

@mixin slider-track-orientation($handle-size, $track-size, $vertical: false) {
  $slider-track-offset: ($handle-size - $track-size) / 2;

  @if $vertical == false {
    top: $slider-track-offset;
    right: 0;
    left: 0;
    height: $track-size;
  } @else {
    top: 0;
    bottom: 0;
    left: $slider-track-offset;
    width: $track-size;
    height: auto; // override the non-vertical rule above
  }
}

@mixin slider-label-orientation($label-offset, $vertical: false) {
  @if $vertical == false {
    transform: translate(-50%, $label-offset);
  } @else {
    transform: translate($label-offset, 50%);
  }
}
