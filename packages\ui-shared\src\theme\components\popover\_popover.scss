// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "./common";

$popover-width: $pt-grid-size * 35 !default;

.#{$ns}-popover {
  @include popover-sizing(
    $arrow-square-size: 30px,
    $arrow-offset: 4px,
    $arrow-target-offset: -4px
  );
  @include popover-appearance(
    $popover-background-color,
    inherit,
    $pt-popover-box-shadow,
    $pt-drop-shadow-opacity,
    $pt-border-shadow-opacity
  );
  @include scale-transition();
  display: inline-block;
  z-index: $pt-z-index-overlay;
  border-radius: $pt-border-radius;

  .#{$ns}-popover-content {
    position: relative;
    border-radius: $pt-border-radius;
  }

  &.#{$ns}-popover-content-sizing {
    .#{$ns}-popover-content {
      max-width: $popover-width;
      padding: $pt-grid-size * 2;
    }

    // only inline popovers get a width if this class is applied.
    // note that an inline overlay will be an immediate next sibling
    // of the popover target as of Blueprint 2.0.
    .#{$ns}-popover-target + .#{$ns}-overlay & {
      width: $popover-width;
    }
  }

  &.#{$ns}-minimal {
    // popovers with no obvious trigger will never have margin because the arrow
    // is hidden so it is safe to remove in all cases always
    margin: 0 !important; // stylelint-disable-line declaration-no-important

    .#{$ns}-popover-arrow {
      display: none;
    }

    &.#{$ns}-popover {
      @include react-transition(
        "#{$ns}-popover",
        (transform: scale(1) scale(1)),
        $duration: $pt-transition-duration,
        $after: "> &"
      );
    }
  }

  &.#{$ns}-dark,
  .#{$ns}-dark & {
    @include popover-appearance(
      $dark-popover-background-color,
      inherit,
      $pt-dark-popover-box-shadow,
      $pt-dark-drop-shadow-opacity,
      $pt-dark-border-shadow-opacity
    );
  }
}

// the box-shadow under the arrow SVG paths
.#{$ns}-popover-arrow::before {
  display: block;
  position: absolute;
  transform: rotate(45deg);
  border-radius: $pt-border-radius - 1;
  content: "";
}

// remove arrow when popover is pinned inside its container
.#{$ns}-tether-pinned .#{$ns}-popover-arrow {
  display: none;
}

.#{$ns}-popover-backdrop {
  background: rgba($white, 0);
}

.#{$ns}-transition-container {
  @include fade-transition();

  // ensure element size exactly equals its children, no edge-case 4px spacing!
  // (try removing this property with Slider content in example)
  display: flex;
  z-index: $pt-z-index-overlay;

  &:focus {
    outline: none;
  }

  // popover content should ignore pointer events during a popover's exit transition
  &.#{$ns}-popover-leave .#{$ns}-popover-content {
    pointer-events: none;
  }

  // Popper.js applies this attribute when the target fully leaves boundaries
  &[data-x-out-of-boundaries] {
    display: none;
  }
}

span.#{$ns}-popover-target {
  // avoid `inline` elements as they won't account for padding etc
  display: inline-block;

  // this is important for span tag as default inline display height only includes text.
  // div tag can be used for display: block, which works fine.
}

.#{$ns}-popover-wrapper.#{$ns}-fill {
  width: 100%;
}
