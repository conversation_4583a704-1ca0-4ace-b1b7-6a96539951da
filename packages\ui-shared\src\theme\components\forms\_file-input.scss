// Copyright 2016 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "../button/common";
@import "../../common/mixins";

/*
File input

Markup:
<label class="#{$ns}-file-input {{.modifier}}">
  <input type="file" {{:modifier}}/>
  <span class="#{$ns}-file-upload-input">Choose file...</span>
</label>

:disabled - Disabled
.#{$ns}-large - Larger size
.#{$ns}-fill - Take up full width of parent element
.#{$ns}-file-input-has-selection - User has made a selection

Styleguide file-input
*/

$file-input-button-width: $pt-grid-size * 7 !default;
$file-input-button-width-large: $pt-grid-size * 8.5 !default;
$file-input-button-padding: ($pt-input-height - $pt-button-height-small) / 2 !default;
$file-input-button-padding-large: ($pt-input-height-large - $pt-button-height) / 2 !default;

.#{$ns}-file-input {
  display: inline-block;
  position: relative;
  cursor: pointer;
  height: $pt-input-height;

  input {
    opacity: 0;
    margin: 0;
    min-width: $pt-grid-size * 20;

    // unlike other form controls that directly style native elements,
    // pt-file-input wraps and hides the native element for better control over
    // visual styles. to disable, we need to disable the hidden child input, not
    // the surrounding wrapper. @see https://github.com/palantir/blueprint/issues/689
    // for gory details.
    &:disabled + .#{$ns}-file-upload-input,
    &.#{$ns}-disabled + .#{$ns}-file-upload-input {
      @include pt-input-disabled();

      &::after {
        @include pt-button-disabled();
      }

      .#{$ns}-dark & {
        @include pt-dark-input-disabled();

        &::after {
          @include pt-dark-button-disabled();
        }
      }
    }
  }

  &.#{$ns}-file-input-has-selection {
    .#{$ns}-file-upload-input {
      color: $pt-text-color;
    }

    .#{$ns}-dark & .#{$ns}-file-upload-input {
      color: $pt-dark-text-color;
    }
  }

  &.#{$ns}-fill {
    width: 100%;
  }

  &.#{$ns}-large,
  .#{$ns}-large & {
    height: $pt-input-height-large;
  }

  .#{$ns}-file-upload-input-custom-text::after {
    content: attr(#{$ns}-button-text);
  }
}

.#{$ns}-file-upload-input {
  @include pt-input();
  @include overflow-ellipsis();
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  padding-right: $file-input-button-width + $input-padding-horizontal;
  color: $pt-text-color-disabled;
  user-select: none;

  &::after {
    @include pt-button();
    @include pt-button-height($pt-button-height-small);
    @include overflow-ellipsis();
    position: absolute;
    top: 0;
    right: 0;
    margin: $file-input-button-padding;
    border-radius: $pt-border-radius;
    width: $file-input-button-width;
    text-align: center;
    line-height: $pt-button-height-small;
    content: "Browse";
  }

  &:hover::after {
    @include pt-button-hover();
  }

  &:active::after {
    @include pt-button-active();
  }

  .#{$ns}-large & {
    @include pt-input-large();
    padding-right: $file-input-button-width-large + $input-padding-horizontal;

    &::after {
      @include pt-button-height($pt-button-height);
      margin: $file-input-button-padding-large;
      width: $file-input-button-width-large;
      line-height: $pt-button-height;
    }
  }

  .#{$ns}-dark & {
    @include pt-dark-input();
    color: $pt-dark-text-color-disabled;

    &::after {
      @include pt-dark-button();
    }

    &:hover::after {
      @include pt-dark-button-hover();
    }

    &:active::after {
      @include pt-dark-button-active();
    }
  }
}

// hack to force the button shadow to display since
// it doesn't render correct via the `pt-button` mixin
// stylelint-disable-next-line
.#{$ns}-file-upload-input::after { box-shadow: $button-box-shadow; }
