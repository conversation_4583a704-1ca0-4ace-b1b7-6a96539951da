name: Deploy Integrations
description: Deploys integrations

input:
  environment:
    type: choice
    description: 'Environment to deploy to'
    required: true
    options:
      - staging
      - production
  extra_filter:
    type: string
    description: 'Pnpm additional filters to select integrations to deploy'
    required: false
    default: ''
  force:
    type: boolean
    description: 'Force re-deploying integrations'
    default: false
    required: false
  dry_run:
    type: boolean
    description: 'Ask the backend to perform validation without actually deploying'
    default: false
    required: false
  sentry_auth_token:
    description: 'Sentry auth token'
    required: true
  token_cloud_ops_account:
    description: 'Cloud Ops account token'
    required: true
  cloud_ops_workspace_id:
    description: 'Cloud Ops workspace id'
    required: true

runs:
  using: 'composite'
  steps:
    - name: List Sentry Integrations
      id: list_sentry_integrations
      shell: bash
      run: |
        script_path="./.github/scripts/ls-sentry-integrations.sh"
        chmod +x $script_path
        filter=$($script_path)
        echo "::set-output name=filter::$filter"
    - name: Inject SourceMaps
      shell: bash
      run: pnpm -r --stream ${{ steps.list_sentry_integrations.outputs.filter }} exec sentry-cli sourcemaps inject .botpress/dist
    - name: Upload SourceMaps
      shell: bash
      run: pnpm -r --stream ${{ steps.list_sentry_integrations.outputs.filter }} exec sentry-cli sourcemaps upload --release=${{ github.sha }} --url-prefix '~' .botpress/dist
      env:
        SENTRY_AUTH_TOKEN: ${{ inputs.sentry_auth_token }}
        SENTRY_ORG: botpress-rm
        SENTRY_RELEASE: ${{ github.sha }}
    - name: Deploys Integrations
      env:
        ENVIRONMENT: ${{ inputs.environment }}
        TOKEN_CLOUD_OPS_ACCOUNT: ${{ inputs.token_cloud_ops_account }}
        CLOUD_OPS_WORKSPACE_ID: ${{ inputs.cloud_ops_workspace_id }}
        SENTRY_RELEASE: ${{ github.sha }}
        SENTRY_ENVIRONMENT: ${{ inputs.environment }}
      shell: bash
      run: |
        api_url="${{ inputs.environment == 'staging' && 'https://api.botpress.dev' || 'https://api.botpress.cloud' }}"

        # login

        echo "### Logging in to $api_url ###"
        pnpm bp login -y --api-url $api_url --workspaceId "$CLOUD_OPS_WORKSPACE_ID" --token "$TOKEN_CLOUD_OPS_ACCOUNT"

        # deploy

        redeploy=${{ inputs.force == 'true' && 1 || 0 }}
        dryrun="${{ inputs.dry_run == 'true' && '--dryRun' || '' }}"
        is_dry_run=${{ inputs.dry_run == 'true' && 1 || 0 }}
        all_filters="-F '{integrations/*}' -F '!asana' -F '!charts' ${{ inputs.extra_filter }}"
        list_integrations_cmd="pnpm list $all_filters --json"
        integration_paths=$(eval $list_integrations_cmd | jq -r "map(".path") | .[]")

        for integration_path in $integration_paths; do
            integration=$(basename $integration_path)
            exists=$(./.github/scripts/integration-exists.sh $integration)

            base_command="bp deploy -v -y --noBuild --public --allowDeprecated $dryrun"

            upload_sandbox_scripts=false
            if [ $exists -eq 0 ]; then
                echo -e "\nDeploying integration: ### $integration ###\n"
                pnpm retry -n 2 -- pnpm -F "{integrations/$integration}" -c exec -- "$base_command"
                upload_sandbox_scripts=true
            elif [ $redeploy -eq 1 ]; then
                echo -e "\nRe-deploying integration: ### $integration ###\n"
                pnpm retry -n 2 -- pnpm -F "{integrations/$integration}" -c exec -- "$base_command"
                upload_sandbox_scripts=true
            else
                echo -e "\nSkipping integration: ### $integration ###\n"
            fi

            # upload sandbox scripts
            integration_implements_sandbox=$(./.github/scripts/integration-implements-sandbox.sh $integration)
            if [ $integration_implements_sandbox == "true" ] && [ $upload_sandbox_scripts == "true" ] && [ $is_dry_run -eq 0 ]; then
                echo -e "\nUploading integration sandbox scripts\n"
                base_upload_command="uploadSandboxScripts --apiUrl=$api_url --workspaceId=$CLOUD_OPS_WORKSPACE_ID --token=$TOKEN_CLOUD_OPS_ACCOUNT --userEmail=<EMAIL>"
                pnpm retry -n 2 -- pnpm -F "{integrations/$integration}" run -- $base_upload_command
            fi
        done
