// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";

/*
Text inputs

Markup:
<input class="#{$ns}-input {{.modifier}}" {{:modifier}} type="text" placeholder="Text input" dir="auto" />

:disabled - Disabled
:readonly - Readonly
.#{$ns}-round - Rounded ends
.#{$ns}-large - Larger size
.#{$ns}-small - Small size
.#{$ns}-intent-primary - Primary intent
.#{$ns}-intent-success - Success intent
.#{$ns}-intent-warning - Warning intent
.#{$ns}-intent-danger - Danger intent
.#{$ns}-fill - Take up full width of parent element

Styleguide input
*/

.#{$ns}-input {
  @include pt-input();

  &.#{$ns}-large {
    @include pt-input-large();
  }

  &.#{$ns}-small {
    @include pt-input-small();
  }

  &.#{$ns}-fill {
    flex: 1 1 auto;
    width: 100%;
  }

  .#{$ns}-dark & {
    @include pt-dark-input();
  }

  @each $intent, $color in $pt-intent-colors {
    &.#{$ns}-intent-#{$intent} {
      @include pt-input-intent($color);

      .#{$ns}-dark & {
        @include pt-dark-input-intent($color);
      }
    }
  }

  &::-ms-clear {
    display: none;
  }
}

/*
Search inputs

Markup:
<div class="#{$ns}-input-group {{.modifier}}">
  <span class="#{$ns}-icon #{$ns}-icon-search"></span>
  <input class="#{$ns}-input" {{:modifier}} type="search" placeholder="Search input" dir="auto" />
</div>

:disabled - Disabled. Also add <code>.#{$ns}-disabled</code> to <code>.#{$ns}-input-group</code> for icon coloring (not shown below).
.#{$ns}-large - Large
.#{$ns}-small - Small

Styleguide input-search
*/

/*
Textareas

Markup:
<textarea class="#{$ns}-input {{.modifier}}" {{:modifier}} dir="auto"></textarea>

:disabled - Disabled
:readonly - Readonly
.#{$ns}-large - Larger font size
.#{$ns}-small - Small font size
.#{$ns}-intent-primary - Primary intent
.#{$ns}-intent-danger  - Danger intent
.#{$ns}-fill  - Take up full width of parent element

Styleguide textarea
*/

// stylelint-disable-next-line selector-no-qualifying-type
textarea.#{$ns}-input {
  max-width: 100%;
  padding: $input-padding-horizontal;

  &,
  &.#{$ns}-large,
  &.#{$ns}-small {
    // override input styles for these modifiers.
    // line-height is needed to center text on <input> but not on multiline <textarea>
    height: auto;
    line-height: inherit;
  }

  &.#{$ns}-small {
    padding: $input-small-padding;
  }

  .#{$ns}-dark & {
    @include pt-dark-input();
  }
}
