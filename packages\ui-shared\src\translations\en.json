{"action": "Action", "actionRequired": "Action Required", "add": "Add", "addVariable": "Add Variable", "advancedSettings": "Advanced Settings", "all": "All", "and": "and", "back": "Back", "bug": "Bug", "builder": "Builder", "cancel": "Cancel", "card": "Card", "change": "Change", "channel": "Channel", "chips": "Chips", "chooseFile": "Choose file...", "close": "Close", "code": "Code", "codeNewAction": "Code New Action", "collapseAll": "Collapse All", "converseApi": "Converse API", "commander": {"backToAdmin": "Back to Admin", "category": {"admin": "Admin", "command": "Command", "external": "External", "module": "<PERSON><PERSON><PERSON>", "studio": "Studio"}, "goTo": "Go to {destination}", "inNewTab": "Open {destination} in a new tab", "links": {"chat": "chat", "documentation": "documentation"}, "openInPopup": "Open chat in a new tab", "searchCommand": "Search for a command", "switchBot": "Switch to bot: {name}", "switchWorkspace": "workspace: {name}", "tabChat": "chat", "toDismiss": "to dismiss", "toNavigate": "to navigate", "toSelect": "to select", "typeCommand": "Type a command", "viewBot": "View bot: {name}", "viewDocumentation": "Consult the documentation in a new tab"}, "confidence": "Confidence", "configuration": "Config", "confirmation": "Confirmation", "confirmPrompt": "Confirmation Required", "content": "Content", "copy": "Copy", "count": "Quantity", "create": "Create", "createdOn": "Created On", "delete": "Delete", "deleteCondition": "Delete Condition", "deleteContent": "Delete Content", "deleteFile": "Delete File", "deleteField": "Delete Field", "deleteNode": "Delete Node", "deletePrompt": "Delete Prompt", "deleteVariable": "Delete Variable", "deleteWorkflow": "Delete Workflow", "description": "Description", "details": "Details", "discard": "Discard", "disabled": "Disabled", "duplicate": "Duplicate", "edit": "Edit", "email": "E-mail", "enable": "Enable", "enabled": "Enabled", "error": "Error", "execute": "Execute", "expandAll": "Expand All", "export": "Export", "exportToJson": "Export to JSON", "failure": "Failure", "filter": "Filter", "filterBy": "Filter <PERSON>", "flows": "Flows", "general": "General", "id": "ID", "if": "If", "ifElse": "If/Else", "image": "Image", "import": "Import", "importJson": "Import JSON", "insert": "Insert", "intent": "intent", "label": "Label", "language": "Language", "learnMore": "Learn more", "library": "Library", "line": "Line", "list": "List", "listen": "Listen", "logs": "Logs", "maximize": "Maximize", "message": "Message", "minimize": "Minimize", "modifiedOn": "Modified On", "move": "Move", "moveDown": "Move Down", "moveUp": "Move Up", "name": "Name", "needsTranslation": "Needs Translation", "notViewingDefaultLang": "You are currently in the {language} version of this chatbot, go back to default language if you want to add elements.", "never": "Never", "next": "Next", "noRowsFound": "No rows found", "of": "of", "ok": "OK", "optional": "Optional", "options": "Options", "or": "or", "overwrite": "Overwrite", "page": "Page", "paste": "Paste", "decision": "Decision", "intents": "Intents", "entities": "Entities", "slots": "Slots", "summary": "Summary", "debugger": "Debugger", "processing": "Processing", "apply": "Apply", "applyPersist": "Apply & Persist", "inspector": "Inspector", "triggers": "Triggers", "actions": "Actions", "pattern": "Regular Expression", "pictures": "Pictures", "pleaseTranslateField": "Please translate this field", "pleaseWait": "Please wait...", "preview": "Preview", "previous": "Previous", "prompt": "Prompt", "quickAddAlternative": "Press Enter while inline to add new alternative quickly", "quickAddAlternativeTags": "Press Enter + Shift while inline to add new alternative quickly", "redo": "Redo", "refresh": "Refresh", "up": "Up", "down": "Down", "rename": "<PERSON><PERSON>", "renameWorkflow": "Rename Workflow", "reset": "Reset", "router": "Router", "rows": "rows", "save": "Save", "saveChanges": "Save Changes", "say": "Say", "search": "Search", "sideMenu": {"alerting": "Alerting", "announcements": "Announcements", "bots": "<PERSON><PERSON>", "collaborators": "Collaborators", "debug": "Debug", "health": "Health", "languages": "Languages", "latestReleases": "Latest Releases", "logs": "Logs", "management": "Management", "modules": "<PERSON><PERSON><PERSON>", "monitoring": "Monitoring", "productionChecklist": "Production Checklist", "roles": "Roles", "serverLicense": "Server License", "sourceControl": "Source Control", "workspace": "Workspace"}, "skills": "Skills", "sortBy": "Sort By", "split": "Split", "status": "Status", "submit": "Submit", "subtitle": "Subtitle", "success": "Success", "suggestions": "Suggestions", "text": "Text", "title": "Title", "tools": "Tools", "topic": "Topic", "topics": "Topics", "translate": "Translate", "trigger": "<PERSON><PERSON>", "type": "Type", "undo": "Undo", "unregistered": "Unregistered", "update": "Update", "upload": "Upload", "usage": "Usage", "date": "Date", "notAvailable": "Not available", "number": "Number", "boolean": "Boolean", "string": "String", "format": "Format", "input": "Input", "output": "Output", "inputs": "Inputs", "outputs": "Outputs", "tolerance": "Tolerance", "enumeration": "Enumeration", "value": "Value", "values": "Values", "strict": "Strict", "exactMatch": "Exact matches only", "medium": "Medium", "moderate": "Moderate", "loose": "Loose", "examples": "Examples", "private": "Private", "uploading": "Uploading", "uncategorized": "Uncategorized", "incomplete": "Incomplete", "active": "Active", "visibility": "Visibility", "timespan": {"lastMonth": "Last Month", "lastWeek": "Last Week", "lastYear": "Last Year", "thisMonth": "This Month", "thisWeek": "This Week", "thisYear": "This Year"}, "contentNotice": {"title": "Changing Shared Content", "message": "This content is used in {count} places. Changing it will affect all places."}}