// This file is automatically generated.
// Please do not change this file!
interface CssExports {
  'actionDialogContent': string;
  'actionSelectItem': string;
  'actionServer': string;
  'actionsWrapper': string;
  'addContentBtn': string;
  'advancedSettingsBtn': string;
  'alignBtnRight': string;
  'category': string;
  'checkboxLabel': string;
  'contentNotice': string;
  'deleteBtn': string;
  'description': string;
  'emptyState': string;
  'fieldError': string;
  'fieldWrapper': string;
  'flexContainer': string;
  'formHeader': string;
  'formLabel': string;
  'formSelect': string;
  'innerWrapper': string;
  'insertBtn': string;
  'missingIcon': string;
  'multipleInputs': string;
  'textInput': string;
  'textarea': string;
  'warning': string;
}
declare var cssExports: CssExports;
export = cssExports;
