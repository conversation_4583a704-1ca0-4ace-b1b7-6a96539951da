// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";

/*
Labels

Markup:
<div>
  <label class="#{$ns}-label {{.modifier}}">
    Label A
    <span class="#{$ns}-text-muted">(optional)</span>
    <input {{:modifier}} class="#{$ns}-input" style="width: 200px;" type="text" placeholder="Text input" dir="auto" />
  </label>
  <label class="#{$ns}-label {{.modifier}}">
    Label B
    <div class="#{$ns}-input-group {{.modifier}}">
      <span class="#{$ns}-icon #{$ns}-icon-calendar"></span>
      <input {{:modifier}} class="#{$ns}-input" style="width: 200px;" type="text" placeholder="Input group" dir="auto" />
    </div>
  </label>
  <label class="#{$ns}-label {{.modifier}}">
    Label C
    <div class="#{$ns}-select {{.modifier}}">
      <select {{:modifier}}>
        <option selected>Choose an item...</option>
        <option value="1">One</option>
      </select>
    </div>
  </label>
</div>

:disabled - Disable the input.
.#{$ns}-disabled - Disabled styles. Input must be disabled separately via attribute.
.#{$ns}-inline - Label and content appear side by side.

Styleguide label
*/

label.#{$ns}-label {
  display: block;
  margin-top: 0;
  margin-bottom: ($pt-grid-size * 1.5);

  .#{$ns}-html-select,
  .#{$ns}-input,
  .#{$ns}-select,
  .#{$ns}-slider,
  .#{$ns}-popover-wrapper {
    display: block;
    margin-top: $pt-grid-size / 2;
    text-transform: none;
  }

  .#{$ns}-button-group {
    margin-top: $pt-grid-size / 2;
  }

  .#{$ns}-select select,
  .#{$ns}-html-select select {
    width: 100%;
    vertical-align: top;
    font-weight: 400;
  }

  &.#{$ns}-disabled {
    &,
    .#{$ns}-text-muted {
      color: $pt-text-color-disabled;
    }
  }

  &.#{$ns}-inline {
    line-height: $pt-input-height;

    .#{$ns}-html-select,
    .#{$ns}-input,
    .#{$ns}-input-group,
    .#{$ns}-select,
    .#{$ns}-popover-wrapper {
      display: inline-block;
      margin: 0 0 0 ($pt-grid-size / 2);
      vertical-align: top;
    }

    .#{$ns}-button-group {
      margin: 0 0 0 ($pt-grid-size / 2);
    }

    .#{$ns}-input-group .#{$ns}-input {
      margin-left: 0;
    }

    &.#{$ns}-large {
      line-height: $pt-input-height-large;
    }
  }

  &:not(.#{$ns}-inline) .#{$ns}-popover-target {
    display: block;
  }

  .#{$ns}-dark & {
    color: $pt-dark-heading-color;

    &.#{$ns}-disabled {
      &,
      .#{$ns}-text-muted {
        color: $pt-dark-text-color-disabled;
      }
    }
  }
}
