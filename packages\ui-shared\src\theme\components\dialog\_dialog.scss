// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "~@blueprintjs/icons/src/icons";
@import "../../common/mixins";
@import "../../common/react-transition";
@import "../../common/variables";

/*
Dialog

Markup:
<!-- this container element fills its parent and centers the .#{$ns}-dialog within it -->
<div class="#{$ns}-dialog-container">
  <div class="#{$ns}-dialog">
    <div class="#{$ns}-dialog-header">
      <span class="#{$ns}-icon-large #{$ns}-icon-inbox"></span>
      <h4 class="#{$ns}-heading">Dialog header</h4>
      <button aria-label="Close" class="#{$ns}-dialog-close-button #{$ns}-button #{$ns}-minimal #{$ns}-icon-cross"></button>
    </div>
    <div class="#{$ns}-dialog-body">
      This dialog hasn't been wired up with any open or close interactions.
      It's just an example of markup and styles.
    </div>
    <div class="#{$ns}-dialog-footer">
      <div class="#{$ns}-dialog-footer-actions">
        <button type="button" class="#{$ns}-button">Secondary button</button>
        <button type="submit" class="#{$ns}-button #{$ns}-intent-primary">Primary button</button>
      </div>
    </div>
  </div>
</div>

Styleguide dialog
*/

$dialog-border-radius: $pt-border-radius * 2 !default;
$dialog-margin: ($pt-grid-size * 3) 0 !default;
$dialog-padding: $pt-grid-size * 2 !default;

.#{$ns}-dialog-container {
  $dialog-transition-props: (
    opacity: (0, 1),
    transform: (scale(0.5), scale(1))
  );

  @include react-transition(
    "#{$ns}-overlay",
    $dialog-transition-props,
    $duration: $pt-transition-duration * 3,
    $easing: $pt-transition-ease-bounce,
    $before: "&",
    $after: "> .#{$ns}-dialog"
  );
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 100%;
  pointer-events: none;
  user-select: none;
}

.#{$ns}-dialog {
  display: flex;
  flex-direction: column;
  margin: $dialog-margin;
  border-radius: $dialog-border-radius;
  box-shadow: $pt-dialog-box-shadow;
  background: $light-gray4;
  padding-bottom:0;
  pointer-events: all;
  user-select: text;
  width: 500px;
  height: auto;
  max-width: 100%;
  max-height: 90%;

  &:focus {
    outline: 0;
  }

  &.#{$ns}-dark,
  .#{$ns}-dark & {
    box-shadow: $pt-dark-dialog-box-shadow;
    background: $pt-dark-app-background-color;
    color: $pt-dark-text-color;
  }
}

.#{$ns}-dialog-header {
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  border-radius: $dialog-border-radius $dialog-border-radius 0 0;
  box-shadow: 0 1px 0 $pt-divider-black;
  background: $white;
  min-height: $pt-icon-size-large + $dialog-padding;
  padding-right: $dialog-padding / 4;
  padding-left: $dialog-padding;

  .#{$ns}-icon-large,
  .#{$ns}-icon {
    flex: 0 0 auto;
    margin-right: $dialog-padding / 2;
    color: $pt-icon-color;
  }

  .#{$ns}-heading {
    @include overflow-ellipsis();
    flex: 1 1 auto;
    margin: 0;
    line-height: inherit;

    &:last-child {
      margin-right: $dialog-padding;
    }
  }

  .#{$ns}-dark & {
    box-shadow: 0 1px 0 $pt-dark-divider-black;
    background: $dark-gray4;

    .#{$ns}-icon-large,
    .#{$ns}-icon {
      color: $pt-dark-icon-color;
    }
  }
}

.#{$ns}-dialog-body {
  flex: 1 1 auto;
  margin: $dialog-padding;
  line-height: $pt-grid-size * 1.8;
}

.#{$ns}-dialog-footer {
  flex: 0 0 auto;
  margin: 0 $dialog-padding;
}

.#{$ns}-dialog-footer-actions {
  display: flex;
  justify-content: flex-end;
  padding-top:10px;
  padding-bottom:10px;

  .btn {
    margin-left: $pt-grid-size;
  }
}
