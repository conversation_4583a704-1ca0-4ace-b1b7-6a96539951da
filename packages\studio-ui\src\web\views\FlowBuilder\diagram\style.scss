.insertNode {
  cursor: crosshair;

  :global(.srd-diagram) {
    cursor: inherit;
  }
}

.floatingInfo {
  position: absolute;
  left: 10px;
  opacity: 0.8;
  z-index: 10;

  .insertMode button {
    margin-top: 10px;
  }
}

:global {
  .bp3-tab {
    text-transform: uppercase;
    font-weight: bold;
  }

  .bp3-tab {
    height: 23px;
    line-height: 23px;
  }

  .bp3-navbar-group {
    height: 23px;
  }

  .bp3-tab[aria-selected='true'] {
    color: var(--shark) !important;
    border-top: 2px solid var(--ocean);
  }

  .bp3-tab[aria-selected='false'] {
    color: #e3e3e3;
  }

  .bp3-tab {
    background: none;
    border: none;
    border-top: 2px solid transparent;
    display: block;
    color: var(--shark);
    font-size: 12px;
    font-weight: bold;
    line-height: 1.25;
    margin: 0;
    padding: 0;
    padding-top: 6px;
    transition: border 0.3s;
  }

  .bp3-tab-indicator-wrapper {
    display: none;
  }

  .bp3-tab-indicator {
    background-color: var(--ocean) !important;
  }
}
