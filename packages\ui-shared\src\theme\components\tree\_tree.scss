// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "../../common/mixins";
@import "~@blueprintjs/icons/src/icons";

/*
Trees

Markup:
<div class="#{$ns}-tree #{$ns}-elevation-0">
  <ul class="#{$ns}-tree-node-list #{$ns}-tree-root">
    <li class="#{$ns}-tree-node #{$ns}-tree-node-expanded">
      <div class="#{$ns}-tree-node-content">
        <span class="#{$ns}-tree-node-caret #{$ns}-tree-node-caret-open #{$ns}-icon-standard"></span>
        <span class="#{$ns}-tree-node-icon #{$ns}-icon-standard #{$ns}-icon-folder-close"></span>
        <span class="#{$ns}-tree-node-label">Label</span>
        <span class="#{$ns}-tree-node-secondary-label">Secondary label</span>
      </div>
      <ul class="#{$ns}-tree-node-list">
        <li class="#{$ns}-tree-node">
          <div class="#{$ns}-tree-node-content">
            <span class="#{$ns}-tree-node-caret-none #{$ns}-icon-standard"></span>
            <span class="#{$ns}-tree-node-icon #{$ns}-icon-standard #{$ns}-icon-document"></span>
          <span class="#{$ns}-tree-node-label">A Document</span>
          </div>
        </li>
        <li class="#{$ns}-tree-node">
          <div class="#{$ns}-tree-node-content">
            <span class="#{$ns}-tree-node-caret-none #{$ns}-icon-standard"></span>
            <span class="#{$ns}-tree-node-icon #{$ns}-icon-standard #{$ns}-icon-document"></span>
            <span class="#{$ns}-tree-node-label">Another Document</span>
          </div>
        </li>
      </ul>
    </li>
  </ul>
</div>

Styleguide tree
*/

$tree-row-height: $pt-grid-size * 3 !default;
$tree-icon-spacing: ($tree-row-height - $pt-icon-size-standard) / 2 !default;

.#{$ns}-tree {
  #{$icon-classes} {
    color: $pt-icon-color;

    @each $intent, $colors in $pt-intent-colors {
      &.#{$ns}-intent-#{$intent} {
        @include intent-color($intent);
      }
    }
  }
}

.#{$ns}-tree-node-list {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.#{$ns}-tree-root {
  // lets us position hover/selection effects based on the width of the whole tree
  position: relative;
  background-color: transparent;
  cursor: default;
  padding-left: 0;
}

@for $i from 0 through 20 {
  .#{$ns}-tree-node-content-#{$i} {
    padding-left: ($tree-row-height - $tree-icon-spacing) * $i;
  }
}

.#{$ns}-tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: $tree-row-height;
  padding-right: $pt-grid-size / 2;

  &:hover {
    background-color: rgba($gray5, 0.4);
  }
}

.#{$ns}-tree-node-caret,
.#{$ns}-tree-node-caret-none {
  min-width: $tree-row-height;
}

.#{$ns}-tree-node-caret {
  @include pt-icon-colors();
  transform: rotate(0deg);
  cursor: pointer;
  padding: $tree-icon-spacing;
  transition: transform ($pt-transition-duration * 2) $pt-transition-ease;

  &.#{$ns}-tree-node-caret-open {
    transform: rotate(90deg);
  }

  // CSS API support
  &.#{$ns}-icon-standard::before {
    content: $pt-icon-chevron-right;
  }
}

.#{$ns}-tree-node-icon {
  position: relative;
  margin-right: $tree-icon-spacing;
}

.#{$ns}-tree-node-label {
  @include overflow-ellipsis();
  flex: 1 1 auto;
  position: relative;
  user-select: none;

  span {
    // prevents spans from interfering with text overflows (e.g. when Tooltips are used in labels)
    display: inline;
  }
}

.#{$ns}-tree-node-secondary-label {
  padding: 0 ($pt-grid-size / 2);
  user-select: none;

  .#{$ns}-popover-wrapper,
  .#{$ns}-popover-target {
    display: flex;
    align-items: center;
  }
}

.#{$ns}-tree-node.#{$ns}-disabled {
  .#{$ns}-tree-node-content {
    background-color: inherit;
    cursor: not-allowed;
    color: $pt-text-color-disabled;
  }

  .#{$ns}-tree-node-caret,
  .#{$ns}-tree-node-icon {
    cursor: not-allowed;
    color: $pt-text-color-disabled;
  }
}

.#{$ns}-tree-node.#{$ns}-tree-node-selected > .#{$ns}-tree-node-content {
  background-color: $pt-intent-primary;

  &,
  #{$icon-classes} {
    color: $white;
  }

  .#{$ns}-tree-node-caret {
    &::before {
      color: rgba($white, 0.7);
    }

    &:hover::before {
      color: $white;
    }
  }
}

.#{$ns}-dark {
  .#{$ns}-tree-node-content:hover {
    background-color: rgba($gray1, 0.3);
  }

  .#{$ns}-tree {
    #{$icon-classes} {
      color: $pt-dark-icon-color;

      @each $intent, $colors in $pt-intent-colors {
        &.#{$ns}-intent-#{$intent} {
          @include intent-color($intent);
        }
      }
    }
  }

  .#{$ns}-tree-node.#{$ns}-tree-node-selected > .#{$ns}-tree-node-content {
    background-color: $pt-intent-primary;
  }
}
