// Copyright 2016 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "../../common/react-transition";

$toast-height: $pt-button-height-large !default;
$toast-min-width: $pt-grid-size * 30 !default;
$toast-max-width: $pt-grid-size * 50 !default;
$toast-margin: $pt-grid-size * 2 !default;

.#{$ns}-toast {
  // toast transition properties
  $enter-translate: (transform: translateY(-$toast-height) translateY(0));
  $leave-blur: (opacity: 0 1, filter: blur($pt-grid-size) blur(0));

  // new toasts slide in from the top
  @include react-transition-phase(
    "#{$ns}-toast",
    "enter",
    $enter-translate,
    $duration: $pt-transition-duration * 3,
    $easing: $pt-transition-ease-bounce,
    $before: "&"
  );
  @include react-transition-phase(
    "#{$ns}-toast",
    "enter",
    $enter-translate,
    $duration: $pt-transition-duration * 3,
    $easing: $pt-transition-ease-bounce,
    $before: "&",
    $after: "~ &"
  );
  // leaving toasts simply fade away
  @include react-transition-phase(
    "#{$ns}-toast",
    "exit",
    $leave-blur,
    $duration: $pt-transition-duration * 3,
    $before: "&"
  );
  // younger siblings of leaving toasts wait a moment before moving to fill gap
  @include react-transition-phase(
    "#{$ns}-toast",
    "exit",
    $enter-translate,
    $delay: $pt-transition-duration / 2,
    $before: "&",
    $after: "~ &"
  );
  display: flex;
  align-items: flex-start;

  // override inline styles (#367): toasts rely on relative positioning for stacking.
  // stylelint-disable-next-line declaration-no-important
  position: relative !important;
  margin: $toast-margin 0 0;
  border-radius: $pt-border-radius;
  box-shadow: $pt-elevation-shadow-3;
  background-color: $white;
  min-width: $toast-min-width;
  max-width: $toast-max-width;

  // toast is interactive even though container isn't
  pointer-events: all;

  .#{$ns}-button-group {
    flex: 0 0 auto;
    padding: ($toast-height - $pt-button-height) / 2;
    padding-left: 0;
  }

  > .#{$ns}-icon {
    margin: ($toast-height - $pt-icon-size-standard) / 2;
    margin-right: 0;
    color: $pt-icon-color;
  }

  &.#{$ns}-dark,
  .#{$ns}-dark & {
    box-shadow: $pt-dark-elevation-shadow-3;
    background-color: $dark-gray5;

    > .#{$ns}-icon {
      color: $pt-dark-icon-color;
    }
  }

  &[class*="#{$ns}-intent-"] {
    a {
      color: rgba($white, 0.7);

      &:hover {
        color: $white;
      }
    }

    > .#{$ns}-icon {
      color: $white;
    }
  }

  // HACKHACK custom colors for buttons on intent background so they always show up nice
  // stylelint-disable declaration-no-important
  &[class*="#{$ns}-intent-"] .#{$ns}-button {
    &,
    &::before,
    .#{$ns}-icon,
    &:active {
      color: rgba($white, 0.7) !important;
    }

    &:focus {
      // blue outline color shows poorly on colored bg
      outline-color: rgba($white, 0.5);
    }

    &:hover {
      background-color: rgba($white, 0.15) !important;
      color: $white !important;
    }

    &:active {
      background-color: rgba($white, 0.3) !important;
      color: $white !important;
    }

    &::after {
      background: rgba($white, 0.3) !important;
    }
  }
  // stylelint-enable declaration-no-important

  // define these last so they override default dark colors above
  @each $intent, $color in $pt-intent-colors {
    &.#{$ns}-intent-#{$intent} {
      background-color: $color;
      color: $white;
    }
  }
}

.#{$ns}-toast-message {
  flex: 1 1 auto;
  padding: centered-text($toast-height);
  word-break: break-word;
}

.#{$ns}-toast-container {
  // override inline overlay styles (#2626)
  // stylelint-disable-next-line declaration-no-important
  display: flex !important;
  flex-direction: column;
  align-items: center;

  position: fixed;
  right: 0;
  left: 0;

  // #975 ensure toasts are on top of everything (esp dialogs)
  z-index: $pt-z-index-overlay * 2;
  // toasts have margin-top so omit it on container

  // prevent container from scrolling as new toasts enter (from bottom)
  overflow: hidden;

  // ensure there's enough space for full box-shadow
  padding: 0 $toast-margin $toast-margin;

  // container will not block clicks on elements behind it
  pointer-events: none;

  &.#{$ns}-toast-container-top {
    top: 0;
  }

  &.#{$ns}-toast-container-bottom {
    flex-direction: column-reverse;
    top: auto;
    bottom: 0;
  }

  &.#{$ns}-toast-container-left {
    align-items: flex-start;
  }

  &.#{$ns}-toast-container-right {
    align-items: flex-end;
  }
}

.#{$ns}-toast-container-bottom .#{$ns}-toast {
  // minimal diff in react-transition styles so we can avoid calling those mixins again
  &.#{$ns}-toast-enter:not(.#{$ns}-toast-enter-active),
  &.#{$ns}-toast-enter:not(.#{$ns}-toast-enter-active) ~ .#{$ns}-toast,
  &.#{$ns}-toast-appear:not(.#{$ns}-toast-appear-active),
  &.#{$ns}-toast-appear:not(.#{$ns}-toast-appear-active) ~ .#{$ns}-toast,
  &.#{$ns}-toast-exit-active ~ .#{$ns}-toast,
  &.#{$ns}-toast-leave-active ~ .#{$ns}-toast {
    transform: translateY($toast-margin + $toast-height);
  }
}
