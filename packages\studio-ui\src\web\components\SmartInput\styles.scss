@import '../../palette.scss';

.editor {
  :global(.public-DraftEditorPlaceholder-root) {
    display: inline;
    position: absolute;
    color: #888;
  }

  cursor: text;
  position: relative;
  box-sizing: border-box;
  border: 1px solid #ddd;
  padding: 5px;
  background: white;

  .insertBtn {
    position: absolute;
    z-index: 5;
    right: 2px;
    top: 2px;
    display: flex;
    transition: background 0.2s ease-in-out;

    &MoreSpacing {
      right: 5px;
      top: 5px;
    }

    span[icon] {
      transition: opacity 0.2s ease-in-out;
      opacity: 0;
    }

    button + button {
      margin-left: 4px;
    }
  }

  &.rtl {
    .insertBtn {
      top: 2px;
      left: 2px;
      right: auto;
      &MoreSpacing {
        top: 5px;
        left: 5px;
        right: auto;
      }
    }
  }

  &:hover, &:focus-within {
    .insertBtn {
      background: rgba(255, 255, 255, 0.9);
      span[icon] {
        opacity: 1;
      }
    }
  }
}

.mentionSuggestions {
  background: #fff;
  border: 1px solid #eee;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  left: 0;
  margin-bottom: 20px;
  max-height: 200px;
  overflow: scroll;
  padding: 6px;
  position: absolute;
  right: 0;
  top: 100%;
  transform: scale(0);
  width: auto;
  z-index: 200;
}

.mentionSuggestions .variable {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  color: $main-text-dark-color;
}

.mentionSuggestions .description {
  font-size: 12px;
  color: #5C7080;
  line-height: 15px;
}

.mentionSuggestionsEntry,
.mentionSuggestionsEntryFocused {
  border-radius: 3px;
  padding: 6px 8px;
  transition: background-color 0.4s cubic-bezier(0.27, 1.27, 0.48, 0.56);

  &:active,
  &.mentionSuggestionsEntryFocused {
    background-color: #E5EAEC;
  }
}

.mentionSuggestionsEntryText {
  display: inline-block;
  margin-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 368px;
  font-size: 0.9em;
  margin-bottom: 0.2em;
}

.mention {
  background-color: #cce7ff;
}

.rtl {
  direction: rtl;
}
