.emtpyStateIcon {
  color: var(--ocean);
}

.headerToolbar {
  margin-bottom: var(--spacing-x-large);
  padding: 0 var(--spacing-medium) !important;
}

.container {
  background-color: white;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 100%;
  width: 100%;
  padding: 10px 15px;
}

.deleteEntity {
  float: right;
  display: none;
  margin-top: 3px;
}

.synonym {
  width: 30%;
  display: none;
}

.chooseContainer {
  padding: 10px 0;
}
