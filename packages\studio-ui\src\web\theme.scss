@import './palette.scss';
@import './react-bootstrap.scss';

a {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, color 0.15s ease-in-out;
}

html {
  height: 100%;
}

body {
  height: 100%;
  font-family: Roboto !important;
  font-weight: 400;
  background-color: $background-color;
  color: $content-color-normal;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-weight: 300;
}

b {
  font-weight: 500;
}

:global(.panel) {
  border: none;
  border-radius: 3px;
  box-shadow: none;
  background-color: #fff;

  :global(.panel-heading) {
    padding: 15px 20px;
    border-color: #f7f7f7;
    background-color: #fff;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 350;
  }
}

:global(.form-control:focus) {
  border-color: #56c0b2;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(86, 192, 178, 0.6);
}

:global(.bp-primary-color-text) {
  color: $primary-color;
}

:global(.bp-primary-color-bg) {
  color: $text-color;
  background-color: $primary-color;
}

:global(.bp-button) {
  color: $btn-text-color;
  background-color: $btn-bg-color;

  min-width: 80px;
  display: inline-block;

  font-size: 15px;
  font-variant: normal;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;

  margin: 1px 1px;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 20px;
  padding-right: 20px;

  border: 1px solid transparent;
  border-radius: 4px;

  outline: 0;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, color 0.15s ease-in-out;

  &:not(:disabled):hover,
  &:not(:disabled):focus,
  &:not(:disabled):active {
    color: $btn-text-color;
    background-color: $btn-bg-color-hover;
    outline: 0;
    box-shadow: none;
    border: 1px solid transparent;
  }

  &:disabled {
    opacity: 0.4;
  }
}

:global(.bp-button-default) {
  background-color: $btn-default-bg-color;

  &:hover,
  &:focus {
    background-color: $btn-default-bg-color-hover;
  }
}

:global(.bp-button-danger) {
  background-color: $btn-danger-bg-color;

  &:hover,
  &:focus {
    background-color: $btn-danger-bg-color-hover;
  }
}

:global(.react-autosuggest__container) {
  position: relative;
}

:global(.react-autosuggest__input) {
  width: 240px;
  height: 30px;
  padding: 10px 20px;
  font-family: Roboto;
  font-weight: 300;
  font-size: 16px;
  border: 1px solid #aaa;
  border-radius: 4px;
}

:global(.react-autosuggest__input--focused) {
  outline: none;
}

:global(.react-autosuggest__input--open) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

:global(.react-autosuggest__suggestions-container) {
  display: none;
}

:global(.react-autosuggest__suggestions-container--open) {
  display: block;
  position: absolute;
  top: 51px;
  width: 280px;
  border: 1px solid #aaa;
  background-color: #fff;
  font-family: Roboto;
  font-weight: 300;
  font-size: 16px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  z-index: 2;
}

:global(.react-autosuggest__suggestions-list) {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

:global(.react-autosuggest__suggestion) {
  cursor: pointer;
  padding: 10px 20px;
}

:global(.react-autosuggest__suggestion--highlighted) {
  background-color: #ddd;
}

:global(.tooltip-inner) {
  border-radius: 0px;
}

:global(.bp3-tooltip .bp3-popover-content) {
  padding: 5px 8px !important;
}

:global(#menuOverlayPortal > div) {
  z-index: 100;
}
