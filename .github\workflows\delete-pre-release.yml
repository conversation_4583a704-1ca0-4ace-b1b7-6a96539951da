name: Branch deleted
on: delete

jobs:
  remove:
    # Do not run on dependabot branches
    if: github.event.ref_type == 'branch' && startsWith(github.event.ref, 'dependabot/') != true
    name: Delete Pre Release Binaries
    runs-on: ubuntu-latest
    steps:
      - name: Extract Informations
        id: info
        uses: botpress/gh-actions/extract_info@v1
        with:
          branch: ${{ github.event.ref }}

      - name: Debug
        run: |
          echo if this action succeeds this binary will be deleted
          echo s3://botpress-dev-bins/studio/${{ steps.info.outputs.branch_sanitized }}

      - name: Install python3
        uses: actions/setup-python@v1
        with:
          python-version: '3.x'
          architecture: 'x64'

      - name: Install Tools
        run: |
          pip install awscli

      - name: Delete Binaries
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          aws s3 rm --recursive s3://botpress-dev-bins/studio/${{ steps.info.outputs.branch_sanitized }}
