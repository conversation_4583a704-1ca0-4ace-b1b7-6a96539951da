.flexContainer {
  display: flex;
  margin-bottom: 5px;
}

.flexContainer > div:first-child {
  min-width: 0;
  width: 100%;
}

.fieldContainer {
  margin-bottom: 20px;
}

.toggleLink {
  text-align: right;
  margin-top: 8px;
  font-size: 12px;
  display: inline-block;
  width: 100%;
}

.fieldError {
  display: inline;
}

.italic {
  font-style: italic;
  word-wrap: break-word;
}

.expressionWrapper {
  line-height: normal !important;
  overflow: hidden;
  position: relative;
  padding: 10px 0;

  &Actions {
    position: absolute;
    right: 5px;
    top: 0px;
  }
}
