.toolbar {
  background: #fff;
  border-radius: 4px;
  padding: var(--spacing-small);
  position: absolute;
  left: var(--spacing-large);
  top: 50%;
  transform: translate(0, -50%);
}

.toolItem {
  align-items: center;
  background: var(--bg);
  border: 1px solid transparent;
  border-radius: 5px;
  color: var(--shark);
  cursor: grab;
  display: flex;
  height: 30px;
  justify-content: center;
  line-height: 0;
  margin-top: var(--spacing-small);
  width: 30px;
  transition: background 0.3s, border 0.3s;

  &:first-of-type {
    margin: 0;
  }

  &:active {
    background: var(--hover-ocean);
    border-color: var(--ocean);
    cursor: grabbing;

    /* Fixes drag border radius issue */
    opacity: 0.999;
  }
}
