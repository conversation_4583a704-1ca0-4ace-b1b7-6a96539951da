$status-bar-height: 30px;

.container {
  position: relative;
  height: 100%;
}

.fullsize,
.fullsize > :where(div) {
  height: 100%;
}

.yOverflowScroll {
  overflow-y: scroll;
}

.container :global(.Resizer.vertical) {
  width: 11px;
  margin: 0 -5px;
  border-left: 5px solid rgba(255, 255, 255, 0);
  border-right: 5px solid rgba(255, 255, 255, 0);
  box-sizing: border-box;
  background: transparent;
  opacity: 0.5;
  z-index: 1;
  background-clip: padding-box;
  cursor: col-resize;

  &:hover {
    -webkit-transition: all 2s ease;
    transition: all 2s ease;
    border-left: 5px solid rgba(0, 0, 0, 0.2);
    border-right: 5px solid rgba(0, 0, 0, 0.2);
  }
}

.container :global(.SplitPane) {
  height: calc(100vh);
}

.sidePanel {
  background-color: #ffffff;
  height: 100%;
  overflow-y: auto;
  padding-bottom: 20px;
}

.sidePanel_hidden {
  .sidePanel {
    display: none;
  }
}

.sidePanel_section {
  height: 35px;
  padding: 2px 5px;
  color: #333333;
  background: #dbdfe2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;

  .action_button {
    cursor: pointer;
    color: #333333;

    margin-left: 5px;

    &:hover {
      color: #888;
    }
  }
  .action_disabled {
    &:hover {
      color: #333333;
    }
  }
}

.splashScreen {
  height: 100%;
  background: #fff;
  color: #444;
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  overflow: auto;

  svg {
    font-size: 250px;
    margin-bottom: -50px;
  }

  > div {
    width: 50%;
    text-align: center;
    font-size: 16px;
    padding-bottom: 20px;

    p:first-of-type {
      margin-bottom: 20px;
    }

    kbd {
      color: #333;
      background: #ddd;
    }
  }
}

.infoTooltip {
  opacity: 0.5;
  cursor: auto;

  &:hover {
    opacity: 1;
  }
}

.searchBar {
  padding: 10px 5px;
}

.itemList {
  position: relative;
  overflow-y: auto;
  //TODO: fix scrollbar
  //height: calc(100vh - #{$status-bar-height});

  .item {
    display: flex;
    padding: 2px 5px;
    cursor: pointer;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    &:first-child {
      border-top: 1px solid #eee;
    }

    .label {
      padding: 5px;
      white-space: nowrap;
      flex-grow: 1;
    }

    .right {
      display: none;
    }

    &:hover {
      background: #eee;
      .right {
        display: flex;
        align-items: center;
      }
    }

    &.active {
      font-weight: bold;
    }
  }
}

.itemListSelected {
  font-weight: bold;
  background: #eee;
}

.toolbar {
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
  padding: 2px 0 3px 15px;
  display: flex;
}

.rightButtons {
  margin-left: auto;
}

.imagePreview {
  width: 140px;
}

.markdownRenderer p {
  margin: 0px;
}
