name: Pre-Release
on:
  pull_request:
    types: [synchronize, review_requested]

jobs:
  pre_release_bin:
    # Do not run on dependabot branches
    if: startsWith(github.head_ref, 'dependabot/') != true

    name: Pre Release Binaries
    runs-on: ubuntu-latest
    steps:
      - name: Get Branch Name
        id: branch
        run: |
          echo "::set-output name=branch_name::$(echo ${GITHUB_HEAD_REF})"

      - name: Extract Informations
        id: info
        uses: botpress/gh-actions/extract_info@v1
        with:
          branch: ${{ steps.branch.outputs.branch_name }}

      - name: Debug
        run: |
          echo if this action succeeds a binary will be available at
          echo s3://botpress-dev-bins/studio/${{ steps.info.outputs.branch_sanitized }}

      - name: Checkout Code
        uses: actions/checkout@master

      - uses: actions/setup-node@v2
        with:
          node-version: '12.18.1'
          cache: 'yarn'

      - name: Install python3
        uses: actions/setup-python@v1
        with:
          python-version: '3.x'
          architecture: 'x64'

      - name: Install Tools
        run: |
          pip install awscli
      - name: Git Unshallow
        run: git fetch --prune --unshallow

      - name: Get Release Details
        id: release_details
        uses: botpress/gh-actions/get_release_details@v1

      - name: Fetch Node Packages
        run: yarn --immutable

      - name: Build Project
        run: yarn build
        env:
          NODE_ENV: production

      - name: Package Binaries
        run: yarn package

      - name: Rename Binary Files
        uses: botpress/gh-actions/rename_binaries@v1
        with:
          subname: ${{ steps.info.outputs.branch_sanitized }}

      - name: Publish Binaries
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          aws s3 sync bin s3://botpress-dev-bins/studio/${{ steps.info.outputs.branch_sanitized }}
