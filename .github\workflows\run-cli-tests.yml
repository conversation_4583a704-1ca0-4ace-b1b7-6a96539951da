name: Run CLI Tests

on:
  pull_request: {}
  workflow_dispatch: {}

permissions:
  id-token: write
  contents: read

jobs:
  run-cli-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    env:
      NODE_OPTIONS: '--max_old_space_size=8192'
    steps:
      - uses: actions/checkout@v2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          extra_filters: '-F @botpress/cli'
      - run: |
          pnpm run -F cli test:e2e -v \
            --api-url https://api.botpress.dev \
            --workspace-id ${{ secrets.STAGING_E2E_TESTS_WORKSPACE_ID }} \
            --workspace-handle clitests \
            --token ${{ secrets.STAGING_TOKEN_CLOUD_OPS_ACCOUNT }} \
            --sdk-path "$(pwd)/packages/sdk" \
            --client-path "$(pwd)/packages/client"
