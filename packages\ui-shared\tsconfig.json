{"compilerOptions": {"experimentalDecorators": true, "esModuleInterop": true, "outDir": "build", "module": "esnext", "lib": ["es6", "dom", "es2016", "es2017"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "resolveJsonModule": true, "target": "es5", "skipLibCheck": true, "allowSyntheticDefaultImports": true, "useUnknownInCatchVariables": false, "strict": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noImplicitAny": false, "baseUrl": "./src", "paths": {"~/*": ["*"], "botpress/sdk": ["../../studio-be/src/sdk/botpress.d.ts"], "common/*": ["../../studio-be/src/common/*"]}}, "include": ["src"], "exclude": []}