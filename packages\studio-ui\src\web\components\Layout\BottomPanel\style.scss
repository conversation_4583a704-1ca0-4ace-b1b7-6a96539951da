.container {
  background: #fff;
  height: 100% !important;
  width: 100%;
  display: flex;
}

.padded {
  border-left: 1px solid lightgray;
  padding: 5px 10px;
  padding-left: 10px;
  width: 100%;
}

.container :global(.bp3-tab-panel) {
  margin-top: 10px;
}

.small {
  width: 250px;
}

.tabs {
  height: 100%;

  :global(.bp3-tab-indicator) {
    background-color: #3c3c3c;
  }

  .tab {
    height: calc(100% - 40px);
    color: 888;
    position: relative;
  }

  .tab[aria-selected='true'] {
    color: white;
  }

  .divide {
    margin: 0 5px;
  }
}

.tabContainer {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: auto;
  position: absolute;
}

.verticalTab {
  margin-top: 5px;

  :global(.bp3-tab) {
    height: 30px !important;
    padding: 5px !important;
  }
}

.inspectorMenu {
  display: flex;

  .menu {
    width: 200px;
    margin-right: 10px;
    border-right: 1px solid lightgray;
  }

  .item {
    white-space: nowrap;
    overflow: hidden;
    height: 20px;
    cursor: pointer;
  }
}

.fullWidth {
  width: 100%;
}

.boxed {
  width: 400px;
}

.flex {
  display: flex;
}

.hidden {
  display: none;
}

.commonButtons {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px 10px;
}
