{"name": "@botpress/studio-be", "version": "0.0.1", "main": "index.js", "author": "Botpress, Inc.", "license": "AGPL-3.0", "private": true, "scripts": {"start": "cross-env NODE_PATH=./out node ./out/index.js", "build": "ts-node  --dir ../../scripts write_metadata && tsc --build", "clean": "rimraf ./out", "watch": "tsc --build --watch"}, "dependencies": {"@apidevtools/json-schema-ref-parser": "^9.0.9", "@botpress/native-extensions": "*", "axios": "^0.25.0", "bluebird": "^3.7.2", "bluebird-global": "^1.0.1", "body-parser": "^1.19.1", "cerialize": "^0.1.18", "chalk": "^4.1.2", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cookie-session": "^1.4.0", "cors": "^2.8.5", "debug": "^4.3.3", "dotenv": "^16.0.0", "errorhandler": "^1.5.1", "eventemitter2": "^5.0.1", "express": "^4.17.2", "express-rate-limit": "^3.5.1", "express-urlrewrite": "^1.4.0", "fs-extra": "^9.1.0", "getos": "^3.2.1", "globrex": "^0.1.2", "http-proxy-middleware": "^2.0.2", "inversify": "^5.1.1", "joi": "^13.6.0", "json-schema-defaults": "^0.4.0", "jsonlint-mod": "^1.7.6", "jsonwebtoken": "^8.5.1", "knex": "0.20.1", "lodash": "^4.17.21", "lodash-decorators": "^6.0.1", "moment": "^2.29.1", "multer": "^1.4.4", "nanoid": "^3.2.0", "ncp": "^2.0.0", "node-machine-id": "^1.1.12", "pg": "^7.8.0", "plur": "^4.0.0", "portfinder": "^1.0.28", "proper-lockfile": "^4.1.2", "reflect-metadata": "^0.1.13", "replace-in-file": "^4.1.1", "rimraf": "^3.0.2", "tar": "^6.1.11", "tmp": "^0.2.1", "verror": "^1.10.1", "vm2": "^3.9.6", "yargs": "^17.3.1", "yn": "^4.0.0"}, "optionalDependencies": {"ioredis": "^4.14.1", "sqlite3": "4.2.0"}, "devDependencies": {"@types/bluebird-global": "^3.5.13", "@types/bluebird-retry": "^0.11.5", "@types/express": "^4.17.13", "@types/fs-extra": "^9.0.13", "@types/joi": "^13.4.5", "@types/lodash": "4.14.178", "@types/node": "^12.18.1", "@types/proper-lockfile": "^4.1.2", "@types/verror": "^1.10.5", "cross-env": "^7.0.3", "typescript": "^4.5.5"}}