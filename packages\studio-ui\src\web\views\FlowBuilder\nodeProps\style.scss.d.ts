// This file is automatically generated.
// Please do not change this file!
interface CssExports {
  'actionDialogContent': string;
  'actionIcons': string;
  'actionList': string;
  'actionSelectItem': string;
  'actionServer': string;
  'actions': string;
  'bottomSection': string;
  'category': string;
  'description': string;
  'endBloc': string;
  'formHeader': string;
  'inspectorTabs': string;
  'item': string;
  'name': string;
  'node': string;
  'nodeBloc': string;
  'returnBloc': string;
  'returnToNodeSection': string;
  'section': string;
  'subflowBloc': string;
  'textFields': string;
  'tip': string;
  'toSubflowSection': string;
}
declare var cssExports: CssExports;
export = cssExports;
