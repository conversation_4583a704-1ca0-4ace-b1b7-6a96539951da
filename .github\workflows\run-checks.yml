name: Run Checks

on: pull_request

permissions:
  id-token: write
  contents: read

jobs:
  run-checks:
    runs-on: depot-ubuntu-22.04-8
    timeout-minutes: 10
    env:
      NODE_OPTIONS: '--max_old_space_size=8192'
    steps:
      - uses: actions/checkout@v2
      - name: Setup
        uses: ./.github/actions/setup
      - run: pnpm run check:dep
      - run: pnpm run check:sherif
      - run: pnpm run check:oxlint
      - run: pnpm run check:format
      - run: pnpm run check:eslint
      - run: pnpm run check:bplint
      - run: pnpm run check:type
      - run: pnpm run test
