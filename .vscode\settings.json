{"editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 2, "eslint.nodePath": ".yarn/sdks", "files.eol": "\n", "prettier.prettierPath": ".yarn/sdks/prettier/index.js", "search.exclude": {"**/.pnp.*": true, "**/.yarn": true}, "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": ".yarn/sdks/typescript/lib", "files.associations": {"*.styl": "scss"}}