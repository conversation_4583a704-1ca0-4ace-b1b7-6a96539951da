@import '../../../palette.scss';

h5 {
  font-weight: 500;
}

.section {
  padding: 5px 0 5px 20px;
  border-left: 3px solid #ddd;
}

.endBloc,
.nodeBloc,
.subflowBloc,
.returnBloc {
  display: inline-block;
  width: 20px;
  height: 15px;
  margin-top: 5px;
}

.returnBloc {
  background-color: darkcyan;
}
.endBloc {
  background-color: #e62eea;
}
.nodeBloc {
  background-color: black;
}
.subflowBloc {
  background-color: blue;
}

.tip {
  font-size: 90%;
  color: #777;
}

.returnToNodeSection {
  input {
    padding: 5px;
  }
}

.toSubflowSection {
  label {
    margin: 5px 0px;
    display: block;
    font-weight: 400;
  }
  input {
    padding: 5px;
  }
}

.node {
  position: relative;

  :global(.panel-heading) {
    text-transform: none;
    padding: 8px 5px 5px 5px;

    :global(.panel-title) {
      font-weight: 600;
      font-size: 12px;
    }
  }

  :global(.tab-content) {
    padding: 10px 0;
  }
}

.node button + button {
  margin-left: 5px;
}

.item {
  padding: 5px 0;
  padding-left: 5px;
  line-break: anywhere;
  max-width: 300px;

  .actions {
    padding: 10px 0;
    display: block;

    a {
      cursor: pointer;
      margin-right: 5px;
    }
  }
}

.item + .item {
  border-top: 1px solid #f9f9f9;
}

.node .name {
  color: #333;
  font-weight: 500;
  border-radius: 0px;
  background: none;
  width: 100% !important;
  border: 1px dashed #d0d0d0;
  padding: 5px 8px;

  &:hover {
    background: #f1f1f1;
  }

  &:focus {
    background: #f5f5f5;
  }
}

.actions {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  padding: var(--spacing-small);

  a {
    padding: 0px 5px;
  }

  button {
    border-radius: 3px;
  }
}

.actionIcons {
  margin-top: 5px;
}

.bottomSection {
  text-align: center;
}

.textFields {
  display: inline-block;
  width: 50% !important;
  margin-top: 3px;

  &:global(.form-control) {
    margin: 0;
    display: inline-block;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: unset;
  }
}

.formHeader {
  position: relative;

  h4 {
    font-size: 22px;
    font-weight: 500;
    color: $main-dark-color;
    margin: 0 0 5px;
  }
}

.actionDialogContent {
  padding: 10px;
}

.actionSelectItem {
  transition: background-color 0.15s ease-in-out;
  border-radius: 2px;
  padding: 3px;
  max-width: 450px;
}

.actionSelectItem:not(:last-child) {
  margin-bottom: 5px;
}

.actionSelectItem:hover {
  cursor: pointer;
  background-color: rgba(167, 182, 194, 0.3);
}

.actionSelectItem .category {
  font-weight: bold;
}

.actionSelectItem .description {
  font-size: small;
}

.actionServer {
  margin: 5px 6px 0 0;
}

.actionList {
  display: flex;
  flex-direction: column;
  line-break: anywhere;
}

.inspectorTabs {
  margin: 10px 0;
}
