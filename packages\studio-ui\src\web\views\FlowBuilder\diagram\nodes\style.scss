:global(.srd-diagram .srd-default-link--path-selected) {
  stroke: var(--gray) !important;
  animation: none;
  stroke-dasharray: 6, 2;
}

:global(.srd-diagram .srd-default-link--path-dragging) {
  stroke: var(--ocean) !important;
  animation: none;
  stroke-dasharray: none;
}

:global(.srd-diagram .node.selected) > .node-container {
  border-color: #028aee !important;
}

.removeLinkButton {
  cursor: pointer;
  rect {
    fill: var(--hover-lighthouse-30);
  }
  .trash {
    fill: var(--lighthouse) !important;
  }
}

.node-container {
  width: 200px;
  background: white;
  border: 1px solid black;
}

.highlightedNode {
  outline: 2px solid var(--ocean) !important;
}

.item {
  word-wrap: break-word;
  text-overflow: ellipsis;
  max-height: 78px;
  overflow-y: scroll;
  border-bottom: 1px solid black;
  font-size: 11px;
}

.section-next .item {
  overflow: unset;
}

.item:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  padding: 3px 8px;
  color: #fff;
  background: #000;

  &.waiting {
    background: #f6bd4f;
    color: #000;
  }

  .subtitle {
    font-size: 80%;
  }
}

.skill-call-node {
  .section-title {
    background: #4580e6;
    display: flex;
  }

  .iconContainer {
    width: 28px;
    padding: 5px 0;
  }
}

.section-onEnter .item,
.section-onReceive .item,
.section-next .item {
  padding: 3px 8px;

  &.fn {
    color: blue;
    font-style: italic;
  }

  &.msg {
    color: #00a653;
  }
}

.section-next {
  border-top: 1px solid black;
}

.section-next .item {
  position: relative;
  background: #ececec;
}

.section-next .item :global(.port) {
  background: rgba(0, 0, 0, 0.2);

  &:global(.selected) {
    background: rgba(0, 0, 0, 0.5);
  }
}

.topPort {
  width: 100%;
  margin-top: -15px;

  :global(.port) {
    margin: 0 auto;
    border: none;
    background: rgba(0, 0, 0, 0.2);

    &:global(.srd-port--selected) {
      background: rgba(0, 0, 0, 0.5);
    }
  }
}

.startPort :global(.port) {
  background-color: lime !important;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.endPort :global(.port) {
  background-color: #e62eea !important;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.missingConnection :global(.port) {
  background-color: red !important;
}

.subflowPort :global(.port) {
  background-color: blue !important;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.returnPort :global(.port) {
  background-color: darkcyan !important;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.section-next .portContainer {
  position: absolute;
  right: -20px;
  top: 3px;
  width: 20px;
}

.section-next .portContainer.portLabel {
  width: 150px;
  right: -150px;
}

.portContainer.startPort {
  height: 15px;
}

.portLabel .label {
  position: absolute;
  padding: 5px;
  right: -20px;
  display: inline;
  border-radius: 3px;
  top: -16px;
  background: rgba(0, 0, 0, 0.08);
}

.subflowPort .label {
  color: blue;
}

.subflowPort .invalidFlow {
  color: red !important;
}

.returnPort .label {
  color: darkcyan;
}

.endPort .label {
  color: #e62eea;
}

.startPort .label {
  color: green;
  font-size: 11px;
  left: -40px;
  right: auto;
  top: -7px;
}
