const TYPES = {
  Logger_Name: Symbol.for('Logger_Name'),
  Logger: Symbol.for('Logger'),
  LoggerProvider: Symbol.for('LoggerProvider'),
  Router: Symbol.for('Router'),
  Database: Symbol.for('Database'),
  ModuleLoader: Symbol.for('ModuleLoader'),
  Botpress: Symbol.for('Botpress'),
  HTTPServer: Symbol.for('HTTPServer'),
  ConfigProvider: Symbol.for('ConfigProvider'),
  BotRepository: Symbol.for('BotRepository'),
  ProjectLocation: Symbol.for('ProjectLocation'),
  GhostService: Symbol.for('GhostService'),
  CMSService: Symbol.for('CMSService'),
  InMemoryDatabase: Symbol.for('InMemoryDatabase'),
  FlowService: Symbol.for('FlowService'),
  BotLoader: Symbol.for('BotLoader'),
  DiskStorageDriver: Symbol.for('DiskStorageDriver'),
  DBStorageDriver: Symbol.for('DBStorageDriver'),
  ObjectCache: Symbol.for('ObjectCache'),
  ActionService: Symbol.for('ActionService'),
  ActionServersService: Symbol.for('ActionServersService'),
  IsPackaged: Symbol.for('IsPackaged'),
  IncomingQueue: Symbol.for('IncomingQueue'),
  OutgoingQueue: Symbol.for('OutgoingQueue'),
  HookService: Symbol.for('HookService'),
  HintsService: Symbol.for('HintsService'),
  EventEngine: Symbol.for('EventEngine'),
  MessagingAPI: Symbol.for('MessagingAPI'),
  DialogEngine: Symbol.for('DialogEngine'),
  DecisionEngine: Symbol.for('DecisionEngine'),
  SessionRepository: Symbol.for('SessionRepository'),
  BotpressAPIProvider: Symbol.for('BotpressAPIProvider'),
  RealtimeService: Symbol.for('RealtimeService'),
  UserRepository: Symbol.for('UserRepository'),
  BotpressAPI: Symbol.for('BotpressAPI'),
  AuthService: Symbol.for('AuthService'),
  InstructionProcessor: Symbol.for('InstructionProcessor'),
  InstructionFactory: Symbol.for('InstructionFactory'),
  FlowNavigator: Symbol.for('FlowNavigator'),
  ActionStrategy: Symbol.for('ActionStrategy'),
  TransitionStrategy: Symbol.for('TransitionStrategy'),
  StrategyProvider: Symbol.for('StrategyProvider'),
  MediaService: Symbol.for('MediaService'),
  MediaServiceProvider: Symbol.for('MediaServiceProvider'),
  JanitorRunner: Symbol.for('JanitorRunner'),
  DialogJanitorRunner: Symbol.for('DialogJanitorRunner'),
  LoggerDbPersister: Symbol.for('LoggerDbPersister'),
  LoggerFilePersister: Symbol.for('LoggerFilePersister'),
  LogsRepository: Symbol.for('LogsRepository'),
  LogJanitorRunner: Symbol.for('LogJanitorRunner'),
  FileCacheInvalidator: Symbol.for('FileCacheInvalidator'),
  BotConfigFactory: Symbol.for('BotConfigFactory'),
  BotConfigWriter: Symbol.for('BotConfigWriter'),
  KeyValueStore: Symbol.for('KeyValueStore'),
  SkillService: Symbol.for('SkillService'),
  AdminService: Symbol.for('AdminService'),
  LicensingService: Symbol.for('LicensingService'),
  AppLifecycle: Symbol.for('AppLifecycle'),
  AuthRolesRepository: Symbol.for('AuthRolesRepository'),
  Statistics: Symbol.for('Statistics'),
  StateManager: Symbol.for('StateManager'),
  ConverseService: Symbol.for('ConverseService'),
  DataRetentionJanitor: Symbol.for('DataRetentionJanitor'),
  DataRetentionService: Symbol.for('DataRetentionService'),
  JobService: Symbol.for('JobService'),
  WorkspaceService: Symbol.for('WorkspaceService'),
  BotService: Symbol.for('BotService'),
  AuthStrategies: Symbol.for('AuthStrategies'),
  MigrationService: Symbol.for('MigrationService'),
  MonitoringService: Symbol.for('MonitoringService'),
  AlertingService: Symbol.for('AlertingService'),
  BotMonitoringService: Symbol.for('BotMonitoringService'),
  EventRepository: Symbol.for('EventRepository'),
  EventCollector: Symbol.for('EventCollector'),
  StatsService: Symbol.for('StatsService'),
  StrategyUsersRepository: Symbol.for('StrategyUsersRepository'),
  TasksRepository: Symbol.for('TasksRepository'),
  WorkspaceUsersRepository: Symbol.for('WorkspaceUsersRepository'),
  WorkspaceInviteCodesRepository: Symbol.for('WorkspaceInviteCodesRepository'),
  LocalActionServer: Symbol.for('LocalActionServer'),
  NLUService: Symbol.for('NLUService'),
  TelemetryRepository: Symbol.for('TelemetryRepository'),
  ActionStats: Symbol.for('ActionStats'),
  LegacyStats: Symbol.for('LegacyStats'),
  RolesStats: Symbol.for('RolesStats'),
  SDKStats: Symbol.for('SDKStats'),
  HooksStats: Symbol.for('HooksStats'),
  ConfigsStats: Symbol.for('ConfigsStats'),
  MessageRepository: Symbol.for('MessageRepository'),
  ConversationRepository: Symbol.for('ConversationRepository'),
  MessageService: Symbol.for('MessageService'),
  ConversationService: Symbol.for('ConversationService'),
  RenderService: Symbol.for('RenderService'),
  MappingRepository: Symbol.for('MappingRepository'),
  QnaService: Symbol.for('QnaService')
}

export { TYPES }
