@charset "utf-8"; // foreign characters ahead (in KSS markup)

// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "common/variables";
@import "common/mixins";
@import "~@blueprintjs/icons/src/icons";

/*
Headings

Markup:
<div>
  <h1 class="@ns-heading">H1 heading</h1>
  <h2 class="@ns-heading">H2 heading</h2>
  <h3 class="@ns-heading">H3 heading</h3>
  <h4 class="@ns-heading">H4 heading</h4>
  <h5 class="@ns-heading">H5 heading</h5>
  <h6 class="@ns-heading">H6 heading</h6>
</div>

Styleguide headings
*/

.#{$ns}-heading {
  @include heading-typography();
  margin: 0 0 $pt-grid-size;
  padding: 0;
}

// tag: (font-size, line-height)
$headings: (
  "h1": (36px, 40px),
  "h2": (28px, 32px),
  "h3": (22px, 25px),
  "h4": (18px, 21px),
  "h5": (16px, 19px),
  "h6": (14px, 16px)
) !default;

@each $tag, $props in $headings {
  %#{$tag} {
    line-height: nth($props, 2);
    font-size: nth($props, 1);
  }

  #{$tag}.#{$ns}-heading {
    @extend %#{$tag};
  }
}

/*
UI text

Markup:
<div class="{{.modifier}}">
  More than a decade ago, we set out to create products that would transform
  the way organizations use their data. Today, our products are deployed at
  the most critical government, commercial, and non-profit institutions in
  the world to solve problems we hadn’t even dreamed of back then.
</div>

.#{$ns}-ui-text - Default Blueprint font styles, applied to the `<body>` tag and available as a class for nested resets.
.#{$ns}-monospace-text - Use a monospace font (ideal for code).
.#{$ns}-running-text - Increase line height ideal for longform text. See [Running text](#core/typography.running-text) below for additional features.
.#{$ns}-text-large - Use a larger font size.
.#{$ns}-text-small - Use a smaller font size.
.#{$ns}-text-muted - Change text color to a gentler gray.
.#{$ns}-text-disabled - Change text color to a transparent, faded gray.
.#{$ns}-text-overflow-ellipsis - Truncate a single line of text with an ellipsis if it overflows its container.

Styleguide ui-text
*/

.#{$ns}-ui-text {
  @include base-typography();
}

.#{$ns}-monospace-text {
  @include monospace-typography();
}

// NOTE: .#{$ns}-text-large defined below after .#{$ns}-running-text

.#{$ns}-text-muted {
  color: $pt-text-color-muted;

  .#{$ns}-dark & {
    color: $pt-dark-text-color-muted;
  }
}

.#{$ns}-text-disabled {
  color: $pt-text-color-disabled;

  .#{$ns}-dark & {
    color: $pt-dark-text-color-disabled;
  }
}

.#{$ns}-text-overflow-ellipsis {
  @include overflow-ellipsis();
}

/*
Running text

Markup:
<div class="@ns-running-text {{.modifier}}">
  <p>
    We build products that make people better at their most important
    work — the kind of work you read about on the front page of the
    newspaper, not just the technology section.
  </p>
  <ul>
    <li>Item the <code>first</code>.</li>
    <li>Item the <strong>second</strong></code>.</li>
    <li>Item the <a href="#core/typography.running-text">third</a>.</li>
  </ul>
  <h3>Scale, Speed, Agility</h3>
  <p>
    A successful data transformation requires the whole organization — users, the IT shop, and
    leadership — to operate in lockstep. With Foundry, the enterprise comes together to
    transform the organization and turn data into a competitive advantage.
  </p>
</div>

.#{$ns}-text-large - Use larger font size.

Styleguide running-text
*/

.#{$ns}-running-text {
  @include running-typography();

  @each $tag, $props in $headings {
    #{$tag} {
      @extend %#{$tag};
      @include heading-typography();
      margin-top: $pt-grid-size * 4;
      margin-bottom: $pt-grid-size * 2;
    }
  }

  hr {
    margin: ($pt-grid-size * 2) 0;
    border: none;
    border-bottom: 1px solid $pt-divider-black;

    .#{$ns}-dark & {
      border-color: $pt-dark-divider-white;
    }
  }

  p {
    margin: 0 0 $pt-grid-size;
    padding: 0;
  }

  blockquote {
    @extend %blockquote;
  }

  code {
    @extend %code;
  }

  kbd {
    @extend %keyboard;
  }

  pre {
    @extend %code-block;
  }

  table {
    @extend %html-table;
  }

  ul,
  ol {
    @extend %list;
  }
}

// NOTE: these must be defined after .@ns-running-text in order to override font-size.
.#{$ns}-text-large {
  font-size: $pt-font-size-large;
  // line-height comes from .@ns-(ui|running)-text
}

.#{$ns}-text-small {
  font-size: $pt-font-size-small;
  // line-height comes from .@ns-(ui|running)-text
}

/*
Links

Simply use an `<a href="">` tag as you normally would. No class is necessary for Blueprint styles.
Links are underlined only when hovered.

Putting an icon inside a link will cause it to inherit the link's text color.

Styleguide typography.links
*/

a {
  text-decoration: none;
  color: $pt-link-color;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
    color: $pt-link-color;
  }

  #{$icon-classes} {
    color: inherit;
  }

  code,
  .#{$ns}-dark & code {
    color: inherit;
  }

  .#{$ns}-dark &,
  .#{$ns}-dark &:hover {
    color: $pt-dark-link-color;

    #{$icon-classes} {
      color: inherit;
    }
  }
}

/*
Preformatted text

Markup:
<div>
  <p>Use the <code class="@ns-code">&lt;code></code> tag for snippets of code.</p>
  <pre class="@ns-code-block">Use the &lt;pre> tag for blocks of code.</pre>
  <pre class="@ns-code-block"><code>// code sample
export function hasModifier(
  modifiers: ts.ModifiersArray,
  ...modifierKinds: ts.SyntaxKind[],
) {
  if (modifiers == null || modifierKinds == null) {
    return false;
  }
  return modifiers.some(m => modifierKinds.some(k => m.kind === k));
}</code></pre>
</div>

Styleguide preformatted
*/

%code {
  @include monospace-typography();

  border-radius: $pt-border-radius;
  box-shadow: inset border-shadow(0.2);
  background: $pt-code-background-color;
  padding: 2px 5px;
  color: $pt-code-text-color;
  font-size: smaller;

  .#{$ns}-dark & {
    box-shadow: inset border-shadow(0.4);
    background: $pt-dark-code-background-color;
    color: $pt-dark-code-text-color;
  }

  a > & {
    // <code> in links. markdown: [`code`](http://url)
    // $pt-link-color does not have good contrast with non-link <code>'s in light theme, so we use a brighter hue
    color: $pt-intent-primary;

    .#{$ns}-dark & {
      color: inherit;
    }
  }
}

%code-block {
  @include monospace-typography();

  display: block;
  margin: $pt-grid-size 0;
  border-radius: $pt-border-radius;
  box-shadow: inset 0 0 0 1px $pt-divider-black;
  background: $pt-code-background-color;
  padding: ($pt-grid-size * 1.3) ($pt-grid-size * 1.5) ($pt-grid-size * 1.2);
  line-height: 1.4;
  color: $pt-text-color;
  font-size: $pt-font-size - 1px;
  word-break: break-all;
  word-wrap: break-word;

  .#{$ns}-dark & {
    box-shadow: inset 0 0 0 1px $pt-dark-divider-black;
    background: $pt-dark-code-background-color;
    color: $pt-dark-text-color;
  }

  > code {
    box-shadow: none;
    background: none;
    padding: 0;
    color: inherit;
    font-size: inherit;
  }
}

.#{$ns}-code {
  @extend %code;
}

.#{$ns}-code-block {
  @extend %code-block;
}

%keyboard {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: $pt-border-radius;
  box-shadow: $pt-elevation-shadow-1;
  background: $white;
  min-width: $pt-button-height-small;
  height: $pt-button-height-small;
  padding: $pt-border-radius ($pt-border-radius * 2);
  vertical-align: middle;
  line-height: $pt-button-height-small;
  color: $pt-text-color-muted;
  font-family: inherit;
  font-size: $pt-font-size-small;

  #{$icon-classes} {
    margin-right: $pt-grid-size / 2;
  }

  .#{$ns}-dark & {
    box-shadow: $pt-dark-elevation-shadow-1;
    background: $dark-gray5;
    color: $pt-dark-text-color-muted;
  }
}

.#{$ns}-key {
  @extend %keyboard;
}

/*
Block quotes

Markup:
<blockquote class="@ns-blockquote">
  Premium Aerotec is a key supplier for Airbus, producing 30 million parts per year,
  which is huge complexity. Skywise helps us manage all the production steps.
  It gives Airbus much better visibility into where the product is in the supply chain,
  and it lets us immediately see our weak points so we can react on the spot.
</blockquote>

Styleguide blockquote
*/

%blockquote {
  margin: 0 0 $pt-grid-size;
  border-left: solid 4px rgba($gray4, 0.5);
  padding: 0 ($pt-grid-size * 2);

  .#{$ns}-dark & {
    border-color: rgba($gray2, 0.5);
  }
}

.#{$ns}-blockquote {
  @extend %blockquote;
}

/*
Lists

Markup:
<ol class="{{.modifier}}">
  <li>Item the first</li>
  <li>Item the second</li>
  <li>
    Item the third
    <ul class="{{.modifier}}">
      <li>Item the fourth</li>
      <li>Item the fifth</li>
    </ul>
  </li>
</ol>

.#{$ns}-list - Add a little spacing between items for readability.
.#{$ns}-list-unstyled - Remove all list styling (including indicators) so you can add your own.

Styleguide lists
*/

%list {
  margin: $pt-grid-size 0;
  padding-left: $pt-grid-size * 3;

  li:not(:last-child) {
    margin-bottom: $pt-grid-size / 2;
  }

  // nested lists
  ol,
  ul {
    margin-top: $pt-grid-size / 2;
  }
}

.#{$ns}-list {
  @extend %list;
}

.#{$ns}-list-unstyled {
  margin: 0;
  padding: 0;
  list-style: none;

  li {
    padding: 0;
  }
}

/*
Right-to-left text

Markup:
<h5 class="@ns-heading">Arabic:</h5>
<p class="@ns-rtl">
  لكل لأداء بمحاولة من. مدينة الواقعة يبق أي, وإعلان وقوعها، حول كل, حدى عجّل مشروط الخاسرة قد.
  من الذود تكبّد بين, و لها واحدة الأراضي. عل الصفحة والروسية يتم, أي للحكومة استعملت شيء. أم وصل زهاء اليا
</p>
<h5 class="@ns-heading">Hebrew:</h5>
<p class="@ns-rtl">
  כדי על עזרה יידיש הבהרה, מלא באגים טכניים דת. תנך או ברית ביולי. כתב בה הטבע למנוע, דת כלים פיסיקה החופשית זכר.
  מתן החלל מאמרשיחהצפה ב. הספרות אנציקלופדיה אם זכר, על שימושי שימושיים תאולוגיה עזה
</p>

Styleguide rtl
*/

.#{$ns}-rtl {
  text-align: right;
}

.#{$ns}-dark {
  color: $pt-dark-text-color;
}
