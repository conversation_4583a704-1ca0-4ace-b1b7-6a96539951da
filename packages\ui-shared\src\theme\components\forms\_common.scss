// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "~@blueprintjs/icons/src/icons";
@import "../../common/variables";
@import "../button/common";

$input-padding-horizontal: $pt-grid-size !default;
$input-small-padding: $pt-input-height-small - $pt-icon-size-standard !default;
$input-font-weight: 400 !default;
$input-transition: box-shadow $pt-transition-duration $pt-transition-ease;

$input-color: $pt-text-color !default;
$input-color-disabled: $button-color-disabled !default;
$input-placeholder-color: $pt-text-color-disabled !default;
$input-background-color: $white !default;
$input-background-color-disabled: rgba($light-gray1, 0.5) !default;
$input-shadow-color-focus: $pt-intent-primary !default;

$dark-input-color: $pt-dark-text-color !default;
$dark-input-color-disabled: $dark-button-color-disabled !default;
$dark-input-placeholder-color: $pt-dark-text-color-disabled !default;
$dark-input-background-color: rgba($black, 0.3) !default;
$dark-input-background-color-disabled: rgba($dark-gray5, 0.5) !default;
$dark-input-shadow-color-focus: $pt-intent-primary !default;

$control-indicator-size: $pt-icon-size-standard !default;
$control-indicator-size-large: $pt-icon-size-large !default;

// avoids edge blurriness for light theme focused default input
// second box-shadow of $pt-input-box-shadow
$input-box-shadow-focus: inset 0 1px 1px rgba($black, $pt-drop-shadow-opacity) !default;

// for best visual results, button group and control group elements should be
// stacked in the following order to ensure sharp edges in all cases and states:

$control-group-stack: (
  // lowest z-index
  "input-disabled",
  "input-default",
  "button-disabled",
  "button-default",
  "button-focus",
  "button-hover",
  "button-active",
  "intent-button-disabled",
  "intent-button-default",
  "intent-button-focus",
  "intent-button-hover",
  "intent-button-active",
  "intent-input-default",
  "input-focus",
  "intent-input-focus",
  "input-group-children",
  "select-caret"
);

// animating shadows requires the same number of shadows in different states
@function input-transition-shadow($color: $input-shadow-color-focus, $focus: false) {
  @if $focus {
    @return
      border-shadow(1, $color, 1px),
      border-shadow(0.3, $color, 3px);
  } @else {
    @return
      border-shadow(0, $color, 0),
      border-shadow(0, $color, 0);
  }
}

@function dark-input-transition-shadow($color: $dark-input-shadow-color-focus, $focus: false) {
  @if $focus {
    @return
      border-shadow(1, $color, 1px),
      border-shadow(1, $color, 1px), // duplicating to minimize browser antialiasing in dark
      border-shadow(0.3, $color, 3px);
  } @else {
    @return
      border-shadow(0, $color, 0),
      border-shadow(0, $color, 0),
      border-shadow(0, $color, 0);
  }
}

@mixin pt-input() {
  @include pt-input-placeholder();

  outline: none;
  border: none;
  border-radius: $pt-border-radius;
  box-shadow: input-transition-shadow($input-shadow-color-focus), $pt-input-box-shadow;
  background: $input-background-color;
  height: $pt-input-height;
  padding: 0 $input-padding-horizontal;
  vertical-align: middle;
  line-height: $pt-input-height;
  color: $input-color;
  font-size: $pt-font-size;
  font-weight: $input-font-weight;
  transition: $input-transition;
  appearance: none;

  &:focus,
  &.#{$ns}-active {
    box-shadow: input-transition-shadow($input-shadow-color-focus, true), $input-box-shadow-focus;
  }

  &[type="search"],
  &.#{$ns}-round {
    border-radius: $pt-input-height;
    // override normalize.css
    box-sizing: border-box;
    padding-left: $pt-grid-size;
  }

  &[readonly] {
    box-shadow: inset 0 0 0 1px $pt-divider-black;
  }

  &:disabled,
  &.#{$ns}-disabled {
    @include pt-input-disabled();
  }
}

@mixin pt-input-placeholder() {
  &::placeholder {
    // normalize.css sets an opacity less than 1, we don't want this
    opacity: 1;
    color: $input-placeholder-color;
  }
}

@mixin pt-input-disabled() {
  box-shadow: none;
  background: $input-background-color-disabled;
  cursor: not-allowed;
  color: $input-color-disabled;
  resize: none;
}

@mixin pt-input-large() {
  height: $pt-input-height-large;
  line-height: $pt-input-height-large;
  font-size: $pt-font-size-large;

  &[type="search"],
  &.#{$ns}-round {
    padding: 0 ($input-padding-horizontal * 1.5);
  }
}

@mixin pt-input-small() {
  height: $pt-input-height-small;
  padding-right: $input-small-padding;
  padding-left: $input-small-padding;
  line-height: $pt-input-height-small;
  font-size: $pt-font-size-small;

  &[type="search"],
  &.#{$ns}-round {
    padding: 0 ($input-small-padding * 1.5);
  }
}

@mixin pt-dark-input-disabled() {
  box-shadow: none;
  background: $dark-input-background-color-disabled;
  color: $dark-input-color-disabled;
}

@mixin pt-dark-input-placeholder() {
  &::placeholder {
    color: $dark-input-placeholder-color;
  }
}

@mixin pt-dark-input() {
  @include pt-dark-input-placeholder();

  box-shadow: dark-input-transition-shadow($dark-input-shadow-color-focus),
              $pt-dark-input-box-shadow;
  background: $dark-input-background-color;
  color: $dark-input-color;

  &:focus {
    box-shadow: dark-input-transition-shadow($dark-input-shadow-color-focus, true),
                $pt-dark-input-box-shadow;
  }

  &[readonly] {
    box-shadow: inset 0 0 0 1px $pt-dark-divider-black;
  }

  &:disabled,
  &.#{$ns}-disabled {
    @include pt-dark-input-disabled();
  }
}

@mixin pt-input-intent($color) {
  box-shadow: input-transition-shadow($color),
              inset border-shadow(1, $color),
              $pt-input-box-shadow;

  &:focus {
    box-shadow: input-transition-shadow($color, true),
                $input-box-shadow-focus;
  }

  &[readonly] {
    box-shadow: inset 0 0 0 1px $color;
  }

  &:disabled,
  &.#{$ns}-disabled {
    box-shadow: none;
  }
}

@mixin pt-dark-input-intent($color) {
  box-shadow: dark-input-transition-shadow($color),
              inset border-shadow(1, $color),
              $pt-dark-input-box-shadow;

  &:focus {
    box-shadow: dark-input-transition-shadow($color, true),
                $pt-dark-input-box-shadow;
  }

  &[readonly] {
    box-shadow: inset 0 0 0 1px $color;
  }

  &:disabled,
  &.#{$ns}-disabled {
    box-shadow: none;
  }
}
