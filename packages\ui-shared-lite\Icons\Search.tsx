// @ts-nocheck
import React, { FC } from 'react'

const Search: FC<{}> = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="70" height="74" viewBox="0 0 70 74">
    <g fill="#3276EA" fillRule="evenodd">
      <path d="M15.467 20.543c.187.43.117.946-.198 1.302-.304.356-.794.503-1.236.367-.083-.024-.153-.06-.223-.098-.069-.037-.128-.086-.175-.135-.07-.05-.128-.11-.175-.172l-.128-.196c-.105-.232-.128-.504-.093-.75.011-.047.023-.11.046-.159.012-.049.035-.098.047-.134.118-.27.327-.504.583-.64.28-.133.607-.146.887-.024.14.05.28.149.385.258.117.11.21.233.28.38" />
      <path d="M15.269 21.845c-.304.356-.794.503-1.237.368-.082-.026-.152-.061-.222-.098-.07-.038-.128-.087-.175-.137-.07-.049-.128-.109-.175-.17l-.128-.197c-.105-.233-.13-.504-.093-.75.011-.048.023-.11.046-.16 0-.023 0-.035.012-.06l.035-.073c.117-.271.327-.503.583-.64.28-.133.607-.147.887-.023.14.05.28.147.385.258.117.11.21.232.28.38.187.43.117.946-.198 1.302M57.916 47.395c-.444.491-1.179.528-1.657.075-.465-.468-.502-1.241-.058-1.744 7.596-8.68 7.328-22.01-.63-30.345-7.957-8.335-20.615-8.556-28.839-.516-8.225 8.04-8.926 21.347-1.61 30.296 7.314 8.96 19.937 10.225 28.723 2.874.5-.431 1.236-.333 1.632.195.41.528.317 1.301-.186 1.718-4.27 3.572-9.357 5.316-14.397 5.316-6.51 0-12.96-2.884-17.534-8.495-8.144-9.955-7.363-24.76 1.784-33.708 9.135-8.937 23.228-8.679 32.072.577 8.855 9.268 9.158 24.096.7 33.757" />
      <path d="M34.495 14.847c-.49.221-.967.467-1.434.725-.455.27-.911.552-1.343.859-.432.307-.85.638-1.26.994-.22.197-.477.283-.747.283-.326 0-.665-.148-.897-.442-.41-.528-.338-1.302.164-1.731.453-.406.943-.786 1.433-1.13.492-.355 1.015-.687 1.54-.994.527-.294 1.074-.577 1.635-.822.594-.272 1.272.024 1.527.65.245.626-.035 1.35-.618 1.608M27.297 21.083c-.292.455-.56.933-.816 1.41-.245.493-.478.996-.69 1.512-.185.466-.617.749-1.073.749-.151 0-.303-.037-.455-.098-.594-.272-.875-.984-.618-1.609.233-.589.503-1.167.794-1.718.28-.552.595-1.105.934-1.62.348-.565 1.083-.712 1.608-.333.537.382.678 1.143.316 1.707" />
      <path d="M16.803 57.694c-.186-.343-.431-.675-.711-.97-.282-.293-.595-.539-.922-.748l4.258-4.481c.512.601 1.06 1.178 1.632 1.72l-4.257 4.479zm-2.321 4.186l-8.167 8.606c-.432.453-1.015.71-1.645.71-.945 0-1.797-.587-2.158-1.508-.363-.92-.165-1.978.512-2.676l8.144-8.58c.455-.456 1.039-.69 1.622-.69.607 0 1.202.246 1.655.713.887.946.9 2.492.037 3.425zM40.837 0c-5.029.025-9.964 1.4-14.35 3.989-.572.333-1.12.7-1.669 1.081-.55.38-1.073.774-1.586 1.179-.012 0-.012.012-.012.012-.513.405-1.015.834-1.493 1.277-1.833 1.668-3.454 3.572-4.83 5.683-.187.27-.362.54-.525.822-.177.27-.339.553-.502.834l-.21.37c-.163.293-.315.577-.467.871-.035.036-.046.085-.06.134-.045.222-.045.454.025.675.094.32.304.578.595.738.56.295 1.236.06 1.527-.516l.584-1.007c.093-.159.176-.307.28-.455.608-1.042 1.296-2.037 2.053-2.981.351-.48.736-.933 1.167-1.387.176-.223.385-.455.608-.663.408-.443.838-.872 1.295-1.266.22-.22.443-.416.676-.601.455-.405.92-.774 1.412-1.142.735-.54 1.492-1.056 2.287-1.522 4.036-2.38 8.575-3.645 13.195-3.67 14.793 0 26.833 12.668 26.833 28.233 0 15.566-12.04 28.233-26.833 28.233-14.794 0-26.834-12.667-26.834-28.233 0-.797.035-1.584.117-2.357.023-.392.07-.773.117-1.165.046-.442.105-.885.186-1.313v-.026c-.011-.577-.42-1.104-.98-1.24-.304-.061-.63 0-.888.196-.245.198-.418.493-.453.823-.072.382-.13.761-.187 1.155-.047.454-.095.907-.128 1.375l-.117 2.552c0 7.144 2.322 13.713 6.229 18.93l-5.412 5.696c-1.074.072-2.125.514-2.952 1.362L1.368 65.27c-1.341 1.41-1.738 3.51-1.004 5.35.724 1.842 2.415 3.033 4.306 3.033 1.235 0 2.437-.528 3.302-1.437l8.177-8.605c.806-.871 1.238-1.976 1.296-3.105l5.402-5.684c4.958 4.114 11.2 6.555 17.99 6.555 16.088 0 29.166-13.76 29.166-30.688C70.003 13.76 56.925 0 40.837 0z" />
    </g>
  </svg>
)

export default Search
