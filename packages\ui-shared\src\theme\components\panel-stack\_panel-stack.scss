// Copyright 2018 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "~@blueprintjs/core/src/common/react-transition";

.#{$ns}-panel-stack {
  position: relative;
  overflow: hidden;
}

.#{$ns}-panel-stack-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  box-shadow: 0 1px $pt-divider-black;
  height: $pt-grid-size * 3;

  .#{$ns}-dark & {
    box-shadow: 0 1px $pt-dark-divider-white;
  }

  // two span children act as spacers to keep the title centered.
  > span {
    display: flex;
    flex: 1;
    align-items: stretch;
  }

  .#{$ns}-heading {
    margin: 0 ($pt-grid-size / 2);
  }
}

.#{$ns}-button.#{$ns}-panel-stack-header-back {
  margin-left: $pt-grid-size / 2;
  padding-left: 0;
  white-space: nowrap;

  .#{$ns}-icon {
    // reduce margins around icon so it fits better in tight header
    margin: 0 2px;
  }
}

.#{$ns}-panel-stack-view {
  @include position-all(absolute, 0);
  display: flex;
  flex-direction: column;
  z-index: 1;

  // border between panels, visible during transition
  margin-right: -1px;
  border-right: 1px solid $pt-divider-black;

  background-color: $white;
  overflow-y: auto;

  .#{$ns}-dark & {
    background-color: $dark-gray4;
  }

  &:nth-last-child(n + 4) {
    display: none;
  }
}

// PUSH transition: enter from right (100%), existing panel moves off left.
.#{$ns}-panel-stack-push {
  @include react-transition-phase(
    "#{$ns}-panel-stack",
    "enter",
    (transform: translateX(100%) translate(0%), opacity: 0 1),
    $easing: ease,
    $duration: $pt-transition-duration * 4
  );
  @include react-transition-phase(
    "#{$ns}-panel-stack",
    "exit",
    (transform: translateX(-50%) translate(0%), opacity: 0 1),
    $easing: ease,
    $duration: $pt-transition-duration * 4
  );
}

// POP transition: enter from left (-50%), existing panel moves off right.
.#{$ns}-panel-stack-pop {
  @include react-transition-phase(
    "#{$ns}-panel-stack",
    "enter",
    (transform: translateX(-50%) translate(0%), opacity: 0 1),
    $easing: ease,
    $duration: $pt-transition-duration * 4
  );
  @include react-transition-phase(
    "#{$ns}-panel-stack",
    "exit",
    (transform: translateX(100%) translate(0%), opacity: 0 1),
    $easing: ease,
    $duration: $pt-transition-duration * 4
  );
}
