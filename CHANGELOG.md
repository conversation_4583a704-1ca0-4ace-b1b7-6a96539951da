## [0.0.68](https://github.com/botpress/studio/compare/v0.0.66...v0.0.68) (2023-11-03)


### Features

* **studio:** add logs filter ([#415](https://github.com/botpress/studio/issues/415)) ([e89fb36](https://github.com/botpress/studio/commit/e89fb3688d91e23fc765745b5429b90f9c2ab844))



## [0.0.67](https://github.com/botpress/studio/compare/v0.0.66...v0.0.67) (2023-06-22)



## [0.0.66](https://github.com/botpress/studio/compare/v0.0.65...v0.0.66) (2023-04-05)


### Bug Fixes

* **studio:** allow context menu on non default language ([#410](https://github.com/botpress/studio/issues/410)) ([651c1af](https://github.com/botpress/studio/commit/651c1af625df932b0989ff1442e85f07974a5582))


### Features

* **studio:** add help button ([#411](https://github.com/botpress/studio/issues/411)) ([8ee6391](https://github.com/botpress/studio/commit/8ee6391a92a6eef157b5dd3f4c6e6d74c0267714))



## [0.0.65](https://github.com/botpress/studio/compare/v0.0.64...v0.0.65) (2022-11-30)


### Bug Fixes

* **studio:** skillbar hidden on non default lang ([#407](https://github.com/botpress/studio/issues/407)) ([6431901](https://github.com/botpress/studio/commit/6431901bc2ca3735521ba17c37489d0fbcac137f))



## [0.0.64](https://github.com/botpress/studio/compare/v0.0.63...v0.0.64) (2022-11-24)


### Features

* **studio:** multilingual captions for skill transitions ([#405](https://github.com/botpress/studio/issues/405)) ([3b50dc2](https://github.com/botpress/studio/commit/3b50dc283a800c70748738581081104796856c7e))



## [0.0.63](https://github.com/botpress/studio/compare/v0.0.61...v0.0.63) (2022-10-24)


### Bug Fixes

* **studio:** studio crash on undefined content item ([#399](https://github.com/botpress/studio/issues/399)) ([1f29187](https://github.com/botpress/studio/commit/1f29187d9d4c044e1c83d4573dfb0d39323bc1ff))
* **ui:** prevent long content type to overflow ([#400](https://github.com/botpress/studio/issues/400)) ([e484773](https://github.com/botpress/studio/commit/e484773d2eea0c28c73cc514c593da5b85539f72))
* properly set content type form step ([#401](https://github.com/botpress/studio/issues/401)) ([f713de1](https://github.com/botpress/studio/commit/f713de1e0e504d73f671a84fd06fee62195f252a))
* **studio:** click on placeholder will focus on input field ([#398](https://github.com/botpress/studio/issues/398)) ([d10d7e5](https://github.com/botpress/studio/commit/d10d7e542568a1652fc5c138c1c7fc95caec50a7))
* **studio:** fix NLU utterances placholder on chrome ([#393](https://github.com/botpress/studio/issues/393)) ([51e003c](https://github.com/botpress/studio/commit/51e003c4e96abe0d2ce8184a45c24cb1a835da3a))
* **studio:** studio crash on undefined content item ([#397](https://github.com/botpress/studio/issues/397)) ([d39bae8](https://github.com/botpress/studio/commit/d39bae8b72043f134d7fe8ded97982111af4ea8d))


### Features

* double click on draggable item to edit ([#402](https://github.com/botpress/studio/issues/402)) ([f6619f1](https://github.com/botpress/studio/commit/f6619f135879eb14c4f270cf6b8d8cb90ab4d963))
* **flow-editor:** Drag n Drop to reorder node element items ([#392](https://github.com/botpress/studio/issues/392)) ([d4f9cf0](https://github.com/botpress/studio/commit/d4f9cf0d06828aaa7585a8ad8562c2be2cc33964))



## [0.0.62](https://github.com/botpress/studio/compare/v0.0.61...v0.0.62) (2022-10-05)


### Bug Fixes

* **studio:** fix NLU utterances placholder on chrome ([#393](https://github.com/botpress/studio/issues/393)) ([51e003c](https://github.com/botpress/studio/commit/51e003c4e96abe0d2ce8184a45c24cb1a835da3a))


### Features

* **flow-editor:** Drag n Drop to reorder node element items ([#392](https://github.com/botpress/studio/issues/392)) ([d4f9cf0](https://github.com/botpress/studio/commit/d4f9cf0d06828aaa7585a8ad8562c2be2cc33964))



## [0.0.61](https://github.com/botpress/studio/compare/v0.0.60...v0.0.61) (2022-08-25)


### Features

* **studio:** allow third party content types to preview images ([#390](https://github.com/botpress/studio/issues/390)) ([d119641](https://github.com/botpress/studio/commit/d119641b42863813eb8850e4369f5779b24de868))



## [0.0.60](https://github.com/botpress/studio/compare/v0.0.59...v0.0.60) (2022-08-22)


### Bug Fixes

* **module:** fix loading code-editor ([#387](https://github.com/botpress/studio/issues/387)) ([b833324](https://github.com/botpress/studio/commit/b83332402865076941cacbef6fb2db0e99a0bd14))



## [0.0.59](https://github.com/botpress/studio/compare/v0.0.58...v0.0.59) (2022-08-18)


### Bug Fixes

* **studio:** fix img renderer for node preview ([#385](https://github.com/botpress/studio/issues/385)) ([3f928c3](https://github.com/botpress/studio/commit/3f928c30e59881f941b42a0332aee30d58cd2bf7))



## [0.0.58](https://github.com/botpress/studio/compare/v0.0.57...v0.0.58) (2022-08-18)


### Bug Fixes

* click on cog to restart server ([#383](https://github.com/botpress/studio/issues/383)) ([22bca64](https://github.com/botpress/studio/commit/22bca64c7ad492421512f70061d3ce7bd21474d3))


### Features

* **studio:** preview content elements from skills ([#382](https://github.com/botpress/studio/issues/382)) ([88121c3](https://github.com/botpress/studio/commit/88121c37f21e0b22ff1f545261c45ab064b3cd30))



## [0.0.57](https://github.com/botpress/studio/compare/v0.0.56...v0.0.57) (2022-07-28)


### Bug Fixes

* **debugger:** fix display of log timestamp ([#375](https://github.com/botpress/studio/issues/375)) ([7d69cae](https://github.com/botpress/studio/commit/7d69caea20cbf09c969331f5c19f91959ddec046))
* **security:** fix access to public envs when bot is unmounted ([#374](https://github.com/botpress/studio/issues/374)) ([7a6ff2e](https://github.com/botpress/studio/commit/7a6ff2e62294a4eeb9a34de619f6ed166b00b959))



## [0.0.56](https://github.com/botpress/studio/compare/v0.0.55...v0.0.56) (2022-07-26)


### Bug Fixes

* **auth:** fix authenticating with cookie storage ([#372](https://github.com/botpress/studio/issues/372)) ([188fb5b](https://github.com/botpress/studio/commit/188fb5b554b7febc9f7b495f57eec475b40631be))



## [0.0.55](https://github.com/botpress/studio/compare/v0.0.54...v0.0.55) (2022-07-18)


### Bug Fixes

* **security:** validate bot access ([#365](https://github.com/botpress/studio/issues/365)) ([fd6bc0c](https://github.com/botpress/studio/commit/fd6bc0cf9594d5213086e43f3890e6b7bf5bce22))
* **studio:** pretty and safe logs ([#368](https://github.com/botpress/studio/issues/368)) ([2bdeeed](https://github.com/botpress/studio/commit/2bdeeedb1a8746ad83948eb47c4946ea5210576c))
* **studio-ui:** fix redirect even when authorized ([#369](https://github.com/botpress/studio/issues/369)) ([2ded358](https://github.com/botpress/studio/commit/2ded358caaa50f532124ea4cda7eccb66d86f371))
* **ui:** redirect to admin on init error ([#366](https://github.com/botpress/studio/issues/366)) ([323c5ee](https://github.com/botpress/studio/commit/323c5eeb90b4bb77a8bce17e1ce8356f821a348d))



## [0.0.54](https://github.com/botpress/studio/compare/v0.0.53...v0.0.54) (2022-07-12)


### Bug Fixes

* **server:** properly return socket transport array to ui ([#363](https://github.com/botpress/studio/issues/363)) ([4eb2695](https://github.com/botpress/studio/commit/4eb2695d25746ffd0b1d3b02c939998b752d93f9))



## [0.0.53](https://github.com/botpress/studio/compare/v0.0.52...v0.0.53) (2022-07-11)


### Bug Fixes

* **debugger:** escape html chars from logs ([#358](https://github.com/botpress/studio/issues/358)) ([15c6510](https://github.com/botpress/studio/commit/15c65105181d1b6618ba0aa5371e55764828436f))
* **flow-editor:** escape html chars from node items previews ([#357](https://github.com/botpress/studio/issues/357)) ([562ebef](https://github.com/botpress/studio/commit/562ebef0b81d75a514e21a901748c613db7daf32))
* **settings:** improve bot name validation ([#355](https://github.com/botpress/studio/issues/355)) ([4c64c49](https://github.com/botpress/studio/commit/4c64c49996642b79f31541db1344fedddea0bfe2))
* **statusBar:** multi-lang training status logic ([#359](https://github.com/botpress/studio/issues/359)) ([e5029f1](https://github.com/botpress/studio/commit/e5029f1d2ff3e597097b8b627b92047d5edb07a0))



## [0.0.52](https://github.com/botpress/studio/compare/v0.0.51...v0.0.52) (2022-06-16)


### Bug Fixes

* **qna:** infinite scroll ([#352](https://github.com/botpress/studio/issues/352)) ([d6565b4](https://github.com/botpress/studio/commit/d6565b40d053e88f61c3c3fcd2c7f95f00ab8583))
* **studio:** fix bot export ([#351](https://github.com/botpress/studio/issues/351)) ([8dacd2d](https://github.com/botpress/studio/commit/8dacd2dc3cac0f35acfe1ecd81f71d480e6ce229))



## [0.0.51](https://github.com/botpress/studio/compare/v0.0.50...v0.0.51) (2022-04-06)


### Bug Fixes

* **actions:** removed tooltip with doc of deprecated api ([#326](https://github.com/botpress/studio/issues/326)) ([72a40c2](https://github.com/botpress/studio/commit/72a40c22128c0adebbcafc9d31c204595641e3ed))
* **debugger:** do not escape user input in debugger logs ([#327](https://github.com/botpress/studio/issues/327)) ([60afbfa](https://github.com/botpress/studio/commit/60afbfa4319051120bf163d002efea5172efd80c))
* **hooks:** send events to core on bot mount and unmount ([#322](https://github.com/botpress/studio/issues/322)) ([fbb4f61](https://github.com/botpress/studio/commit/fbb4f6152c789d06d558174abfa5b59107fb6a7c))



## [0.0.50](https://github.com/botpress/studio/compare/v0.0.49...v0.0.50) (2022-03-25)



## [0.0.49](https://github.com/botpress/studio/compare/v0.0.48...v0.0.49) (2022-03-17)


### Bug Fixes

* **build:** fix package on windows & cmd ([#298](https://github.com/botpress/studio/issues/298)) ([43f2b71](https://github.com/botpress/studio/commit/43f2b712be8762f79eeca1aef5015445441f4c90))
* **nlu:** intent slot formatting ([#295](https://github.com/botpress/studio/issues/295)) ([2d1b01b](https://github.com/botpress/studio/commit/2d1b01b6019d91f0dbb26d23a560c6371e091fb0))
* **qna:** improve slot intent sidebar ui ([#301](https://github.com/botpress/studio/issues/301)) ([b79ebef](https://github.com/botpress/studio/commit/b79ebef2c92832d8e86dd504bb6a70d10425a258))
* **studio:** fix libraries ([#311](https://github.com/botpress/studio/issues/311)) ([c13b5c4](https://github.com/botpress/studio/commit/c13b5c42f31696a3e12de03e2a0ee9989df69286))
* **studio:** proxy redirect ([#302](https://github.com/botpress/studio/issues/302)) ([568ed32](https://github.com/botpress/studio/commit/568ed3255bc747d96aa8d208cdc440a627379ac5))


### Features

* **studio:** allow disabling file listeners for read-only deployments ([#242](https://github.com/botpress/studio/issues/242)) ([efad392](https://github.com/botpress/studio/commit/efad3922c411340690afece925416030046b2945))



## [0.0.48](https://github.com/botpress/studio/compare/v0.0.47...v0.0.48) (2022-02-09)


### Bug Fixes

* **qna:** fix scrolling ([#287](https://github.com/botpress/studio/issues/287)) ([c5c826c](https://github.com/botpress/studio/commit/c5c826cd09eb4ccab949f4af59f6d7af4b50b6b1))
* **studio:** content picker broken ([#283](https://github.com/botpress/studio/issues/283)) ([3a39ebf](https://github.com/botpress/studio/commit/3a39ebf01869f6ab1e768a7836aa3505ceba1d0c))


### Reverts

* **deps:** downgrade fs-extra to v9 ([#284](https://github.com/botpress/studio/issues/284)) ([57ae613](https://github.com/botpress/studio/commit/57ae613039a61e4f377fd78911eaae8b1428a222))



## [0.0.47](https://github.com/botpress/studio/compare/v0.0.46...v0.0.47) (2022-02-07)


### Bug Fixes

* **studio:** fix random error with lang switcher ([#253](https://github.com/botpress/studio/issues/253)) ([1fe38d5](https://github.com/botpress/studio/commit/1fe38d54d7c6cd921d5351596b8ac80d43421896))
* **studio:** prevent back & forth when file is not found ([#249](https://github.com/botpress/studio/issues/249)) ([c22ea75](https://github.com/botpress/studio/commit/c22ea7511797572d64c9a6bd42233e46947b8ce5))
* **studio-be:** fix cache invalidation being discarded by flow service ([#229](https://github.com/botpress/studio/issues/229)) ([e15f83e](https://github.com/botpress/studio/commit/e15f83e2277f783d141374eda28bb80e5ebeb93c))
* **ui:** no model warning in debugger only appears when there's no model ([#257](https://github.com/botpress/studio/issues/257)) ([f66369f](https://github.com/botpress/studio/commit/f66369f847af34f6ab98c25af09dbbd50ed9c609))
* **ui:** zoom in and out from cursor position ([#262](https://github.com/botpress/studio/issues/262)) ([6ed8110](https://github.com/botpress/studio/commit/6ed8110ac0ed1a0105186f0e4fed34138022c514))
* properly display start enterprise trial ([#248](https://github.com/botpress/studio/issues/248)) ([900b067](https://github.com/botpress/studio/commit/900b0674a1302bb05b7858bd8c977615fc12d6f7))


### Features

* **studio:** adds guided tour button to top ([#227](https://github.com/botpress/studio/issues/227)) ([8c0d57f](https://github.com/botpress/studio/commit/8c0d57f0adc35e5323307c46577746f6da349b9b))



## [0.0.46](https://github.com/botpress/studio/compare/v0.0.45...v0.0.46) (2022-01-14)


### Bug Fixes

* **flow:** reverse flow name condition ([a613812](https://github.com/botpress/studio/commit/a613812fca07b044d6710f3e41bd86caddcb4b53))



## [0.0.45](https://github.com/botpress/studio/compare/v0.0.44...v0.0.45) (2022-01-14)


### Bug Fixes

* **copy:** copying skill makes clone but uses same cms content ([79d728e](https://github.com/botpress/studio/commit/79d728e3b959fe649b973e04749651093d97acda))
* **copy:** imperatively create flows bc side-effect selector out of sync ([b957b5b](https://github.com/botpress/studio/commit/b957b5b1f505a1443b9e8b30bf018891dbab1339))
* **flow:** tiny z-index css fix ([b97fbf4](https://github.com/botpress/studio/commit/b97fbf4b916f48cc3bda8e33849d9add3e4ed5a8))
* **layout:** bottom panel maintain size ([3a943a9](https://github.com/botpress/studio/commit/3a943a904cd7ffffd3d305413078907146db2413))
* **nlu:** trim entity occurences on change ([#223](https://github.com/botpress/studio/issues/223)) ([6588327](https://github.com/botpress/studio/commit/658832766e1346d082de9d567b0868de0cdeab06))
* **studio:** add content notice translations ([96e4611](https://github.com/botpress/studio/commit/96e461151a8567f82daadfa739efa2e9dd1d6612))
* **studio:** prevent skills folder with casing ([32823b2](https://github.com/botpress/studio/commit/32823b262baa3133aa7654219ce54f68271801e2))
* **ui:** remove enterprise callout for enterprise user ([72f26ed](https://github.com/botpress/studio/commit/72f26ed55cca3b081270207795fbbf186eb960f5))


### Features

* **content:** add notice when content shared ([ec8ef0c](https://github.com/botpress/studio/commit/ec8ef0cd4cb22029c46087a6cf5c3c375a874e9b))
* **studio:** add warning when editing shared content ([bb8bc30](https://github.com/botpress/studio/commit/bb8bc30c3c739ae7ddbaba8083d2ae18b9f6f429))



## [0.0.44](https://github.com/botpress/studio/compare/v0.0.42...v0.0.44) (2021-12-02)


### Bug Fixes

* **redis:** fix redis only retrying to connect 10 times ([#211](https://github.com/botpress/studio/issues/211)) ([93c294d](https://github.com/botpress/studio/commit/93c294d19a28dc2ed181a3c351c2f4434722ba29))



## [0.0.43](https://github.com/botpress/studio/compare/v0.0.42...v0.0.43) (2021-11-19)


### Bug Fixes

* **debugger:** Voice-Content Type in Emulator ([#163](https://github.com/botpress/studio/issues/163)) ([da4c466](https://github.com/botpress/studio/commit/da4c466a809ebbe722d9863d47f31f9d4474761f))
* **nlu:** trim utterances on server side ([#175](https://github.com/botpress/studio/issues/175)) ([c4188a3](https://github.com/botpress/studio/commit/c4188a34dfc1c2d2575e9a9c8e978ad503923a45))
* **qna:** disable new qna creation on IME input ([#164](https://github.com/botpress/studio/issues/164)) ([d748507](https://github.com/botpress/studio/commit/d7485072c42f2ed294967ff98c005901913ba469))
* **studio:** fix error when renaming node ([#159](https://github.com/botpress/studio/issues/159)) ([0183dfa](https://github.com/botpress/studio/commit/0183dfaca7e5ea07374e06c0a69e9614a4f19ade))
* **studio:** fix regression in utterance parsing ([#200](https://github.com/botpress/studio/issues/200)) ([bce7938](https://github.com/botpress/studio/commit/bce79385f6d4e860506b7f00939cb97a092c9ddc))
* **studio:** fix url when using a different path ([#205](https://github.com/botpress/studio/issues/205)) ([e30b54c](https://github.com/botpress/studio/commit/e30b54c1cbaf0b6914a03a76bf6e146bc136d6e6))
* **studio:** incorrect transition migration after updating skill ([#179](https://github.com/botpress/studio/issues/179)) ([b30c7e3](https://github.com/botpress/studio/commit/b30c7e3cad221103edbf9fbef439354b57b0b39c))
* **studio:** more time for logging that user is logged out ([#192](https://github.com/botpress/studio/issues/192)) ([f7c5035](https://github.com/botpress/studio/commit/f7c5035135e9cad6bb130d2d8dcf8a09ff923906))
* **studio:** no ghost flows when deleting a bot ([#160](https://github.com/botpress/studio/issues/160)) ([60825f3](https://github.com/botpress/studio/commit/60825f3ba764090776fbab84e487a8db1549c8f1))
* **studio:** nodes created during transition drag will properly open node properties ([#154](https://github.com/botpress/studio/issues/154)) ([5d7f28f](https://github.com/botpress/studio/commit/5d7f28f30078c82a0b87a0fc5312ccc1c26ed5a6))
* **studio:** prevent using skills in folde rname ([#157](https://github.com/botpress/studio/issues/157)) ([9e89dcf](https://github.com/botpress/studio/commit/9e89dcf6c06f5215e07c6f53623a64718a3b37be))
* **studio:** warns user about double braces ([#168](https://github.com/botpress/studio/issues/168)) ([371e8ae](https://github.com/botpress/studio/commit/371e8ae17d9c0eaa7d2fec697eed6b17fb634b04))
* **studio:** Warns user he will be logged out ([#185](https://github.com/botpress/studio/issues/185)) ([323e2b2](https://github.com/botpress/studio/commit/323e2b25e221c9c47b4e0ab2c71001afae628804))
* **zoom:** added translations ([c833e62](https://github.com/botpress/studio/commit/c833e62e5ab332b7a477ebe2511c107bb8e483e3))
* **zoom:** apply translations ([ce1e8c7](https://github.com/botpress/studio/commit/ce1e8c7f636c4657ea2dca41b5c6df9410e932f1))
* **zoom:** more spacing around the zoom buttons ([5bcca17](https://github.com/botpress/studio/commit/5bcca1758f772b636c9d41ac1bfa4ded27144e0b))
* hardcoded query in context dropdown ([#176](https://github.com/botpress/studio/issues/176)) ([4e8cb8d](https://github.com/botpress/studio/commit/4e8cb8d1e6a7625868e75013eb3907d625f91cb0))
* subdomain media URL ([0332d21](https://github.com/botpress/studio/commit/0332d2118f157e200d89467c6aeb89f98f482607))


### Features

* **bottomPanel:** add common button on code editor ([#162](https://github.com/botpress/studio/issues/162)) ([e49ec77](https://github.com/botpress/studio/commit/e49ec7728596122484fdadc8a34794ad565b094e))
* **studio:** diagram option to fit the current flow ([df5f64d](https://github.com/botpress/studio/commit/df5f64dd7ef6ba90905960b22554e86fb65ae2b1))
* **studio:** diagram option to fit the current flow ([cb45414](https://github.com/botpress/studio/commit/cb454141cf339a2db192a05499b7020692a3edf5))
* **toolbar:** Add CTA button ([#204](https://github.com/botpress/studio/issues/204)) ([f61b9ca](https://github.com/botpress/studio/commit/f61b9caf56af01d8598aace0d475c70d887d2d3c))



## [0.0.42](https://github.com/botpress/studio/compare/v0.0.41...v0.0.42) (2021-11-09)


### Bug Fixes

* **content:** fix carousel img delete button ([#189](https://github.com/botpress/studio/issues/189)) ([065fb47](https://github.com/botpress/studio/commit/065fb477d92cb51bde7d9873cf751e8d9763de75))
* **nlu:** allow copy paste utterances ([#171](https://github.com/botpress/studio/issues/171)) ([fcd96cb](https://github.com/botpress/studio/commit/fcd96cbea5ce6fac9b1288b7be1c5445de49a124))
* **nlu:** prevent creating an entity with same name as a system ([#158](https://github.com/botpress/studio/issues/158)) ([4d22dd2](https://github.com/botpress/studio/commit/4d22dd2cb5035213c9aecb170e9d2cf0d9b037a9))
* **skills:** dialog overflow on too many params, fixes [#149](https://github.com/botpress/studio/issues/149) ([#151](https://github.com/botpress/studio/issues/151)) ([266cb94](https://github.com/botpress/studio/commit/266cb94c3035c41ac3ec5d7457bef7cd3785f4d3))
* **studio:** added translations in node and flow inspector ([#180](https://github.com/botpress/studio/issues/180)) ([8280b62](https://github.com/botpress/studio/commit/8280b62256deb664a73570a24033e4e8394701fc))
* **studio:** can't lose the bottom debugger panel because of window resize anymore ([#170](https://github.com/botpress/studio/issues/170)) ([54c219a](https://github.com/botpress/studio/commit/54c219a85ddaf185e1528ec8999d12f06d8ea701))
* **studio:** CMS crashes on new language translation ([#166](https://github.com/botpress/studio/issues/166)) ([3a1dbe5](https://github.com/botpress/studio/commit/3a1dbe5701f1cfc3bc0e2d8fef320ed345522cf6))
* **studio:** DEV-1421: Fix rename node ([#165](https://github.com/botpress/studio/issues/165)) ([3b407de](https://github.com/botpress/studio/commit/3b407deb01e47ddfaa5ce07654d5604b2e420026))
* **studio:** display error when no nlu modelId ([#183](https://github.com/botpress/studio/issues/183)) ([644ce44](https://github.com/botpress/studio/commit/644ce448922bf3b0ca33152898d56f5faac6de5e))
* **studio:** fix the smartinput suggestions cursor in contentform ([#161](https://github.com/botpress/studio/issues/161)) ([86ea816](https://github.com/botpress/studio/commit/86ea81651f3f938ae4b193ed60e189310818f745)), closes [#49](https://github.com/botpress/studio/issues/49)
* **studio:** missing translation in debugger panel ([#173](https://github.com/botpress/studio/issues/173)) ([bf57d63](https://github.com/botpress/studio/commit/bf57d63ec3611931aabea7b024f70b6771538bdb))
* **studio:** select dropdown overflowing modals did not render properly - fixes [#137](https://github.com/botpress/studio/issues/137) ([#155](https://github.com/botpress/studio/issues/155)) ([77cc384](https://github.com/botpress/studio/commit/77cc384a2560687cce1e36eb84358ffa90c90d8b))
* **studio:** show webchat by default ([#167](https://github.com/botpress/studio/issues/167)) ([884ebc2](https://github.com/botpress/studio/commit/884ebc24dcd9e1c24c1e803ebd97d486c2031f98))
* small eslint warnings and typings ([5a75434](https://github.com/botpress/studio/commit/5a754348372b813a83ceea5655a254bc98f1a698))


### Features

* adds Segment integration ([#147](https://github.com/botpress/studio/issues/147)) ([ea98ee0](https://github.com/botpress/studio/commit/ea98ee02e85f6b605784e7417dfe2ebd111fb00c))



## [0.0.41](https://github.com/botpress/studio/compare/v0.0.28...v0.0.41) (2021-10-22)


### Bug Fixes

* **qna:** fix context search in QnA ([#141](https://github.com/botpress/studio/issues/141)) ([f8bacf3](https://github.com/botpress/studio/commit/f8bacf355d122e6a6e7edc60c951468f71f96fea))
* **studio:** ignore proxy when calling core ([#142](https://github.com/botpress/studio/issues/142)) ([cf44e91](https://github.com/botpress/studio/commit/cf44e913b23005da2f25b391b1b73d407d9d16c5))



# [0.0.40](https://github.com/botpress/studio/compare/v0.0.39...v0.0.40) (2021-10-18)


### Bug Fixes

* always display search bar on qna ([#136](https://github.com/botpress/studio/issues/136)) ([0220da2](https://github.com/botpress/studio/commit/0220da24965daa3b1e35a4599e7d14e82229a4dc))



## [0.0.39](https://github.com/botpress/studio/compare/v0.0.28...v0.0.39) (2021-10-07)


### Bug Fixes

* **cms:** display unregistered content-types instead of error message ([#81](https://github.com/botpress/studio/issues/81)) ([d3370be](https://github.com/botpress/studio/commit/d3370be41630760ef5abd2625e6c227c70cff629))
* **core:** fix redis pubsub by adding redis scopes ([#83](https://github.com/botpress/studio/issues/83)) ([2ed671f](https://github.com/botpress/studio/commit/2ed671fef6c6f0f6cca6c58a7df193ce9985286f))
* **debugger:** use message ids ([#30](https://github.com/botpress/studio/issues/30)) ([2fc6756](https://github.com/botpress/studio/commit/2fc67569d72188a6776805d0e9e1bc21066fb958))
* **dev:** fix condition ([235389d](https://github.com/botpress/studio/commit/235389d3566b2df29c0244b954c9682dd372c1f5))
* **flow:** fix clearing node action parameters on close ([#91](https://github.com/botpress/studio/issues/91)) ([5ffbb9a](https://github.com/botpress/studio/commit/5ffbb9a191a6e1b2e0d702c8729b1746606e1b98))
* **flow:** fix ctrl + s toast text display ([#127](https://github.com/botpress/studio/issues/127)) ([e46f9a9](https://github.com/botpress/studio/commit/e46f9a9185a3f1508149d52249912071e0f3f001))
* **flow:** fix editing content elements using action modal ([#109](https://github.com/botpress/studio/issues/109)) ([c021286](https://github.com/botpress/studio/commit/c021286a5453c14abe3bc007a432099291afce91))
* **flowbuilder:** fix wait on issue with new rtl procedure ([#98](https://github.com/botpress/studio/issues/98)) ([b0f2db4](https://github.com/botpress/studio/commit/b0f2db48bbb533b8beaa14fbe0cb09b9ecaee0f0))
* **libraries:** fix inexistent folder ([74f8c01](https://github.com/botpress/studio/commit/74f8c016b83e71e2e59e78f37aaf46da8a856d91))
* **libraries:** more fix for distributed usage ([c22d9de](https://github.com/botpress/studio/commit/c22d9de9ebe9819e434abec837e1b53b244ad97a))
* **libraries:** updated logic to use disk first ([#63](https://github.com/botpress/studio/issues/63)) ([84ec937](https://github.com/botpress/studio/commit/84ec9378f0a60f5f2e2f9b6822facf16d42446a5))
* **qna:** fix language dropdown overlay issue  ([#115](https://github.com/botpress/studio/issues/115)) ([056686b](https://github.com/botpress/studio/commit/056686b8e71d94723108eb4136fda2e0e335679a))
* **studio:** add developer cue ([#94](https://github.com/botpress/studio/issues/94)) ([18e8fc2](https://github.com/botpress/studio/commit/18e8fc2a67aabe64418d9fc3dfc1f317e8395539))
* **studio:** added form validation to bot seetings page ([#129](https://github.com/botpress/studio/issues/129)) ([f557516](https://github.com/botpress/studio/commit/f557516df9d0795c116c66496ddb365390bd93f5))
* **studio:** allow changing conent-type when editing node content-element ([#121](https://github.com/botpress/studio/issues/121)) ([4bf2ae2](https://github.com/botpress/studio/commit/4bf2ae21b7a7aa69520aceade8e9c71189b1c564))
* **studio:** bot mig same behavior as core ([#86](https://github.com/botpress/studio/issues/86)) ([6fd2bdc](https://github.com/botpress/studio/commit/6fd2bdcfdcab7468d68be7297e393559df4abcba))
* **studio:** error when state missing in debugger ([#111](https://github.com/botpress/studio/issues/111)) ([31af69f](https://github.com/botpress/studio/commit/31af69fe18b7f2c0e34ca5fd18aaee8a7fcc6f74))
* **studio:** fix delete nodes link issue ([#62](https://github.com/botpress/studio/issues/62)) ([52ff16e](https://github.com/botpress/studio/commit/52ff16e970504106ee085e07285baf4abd9b401f))
* **studio:** fix permissions for libraries ([#119](https://github.com/botpress/studio/issues/119)) ([86e53ad](https://github.com/botpress/studio/commit/86e53adcff929b6b68ec9089af6b69e61462790b))
* **studio:** fix session storage with storage utils ([#22](https://github.com/botpress/studio/issues/22)) ([b292015](https://github.com/botpress/studio/commit/b2920157aacc9ef2ac1d386fb7c67656e0c3e78a))
* **studio:** fix typings for yn signature ([42c11e0](https://github.com/botpress/studio/commit/42c11e0c988e817384a8dda9f0a95a55b035b34a))
* **studio:** move events-related routes ([#56](https://github.com/botpress/studio/issues/56)) ([ccf010f](https://github.com/botpress/studio/commit/ccf010f2b81e2e6aca8ee5b876e84d5f1bde0e2a))
* **studio:** overlapping between search bar & node props side panel ([#37](https://github.com/botpress/studio/issues/37)) ([6e4d841](https://github.com/botpress/studio/commit/6e4d841cd83a0ecfb52d1d77d042a7c588b0fcfe))
* **studio:** prevent creating empty actions ([#118](https://github.com/botpress/studio/issues/118)) ([fed7ac6](https://github.com/botpress/studio/commit/fed7ac66da61b6ac3a5cc0d43ca1106806934e7c))
* **studio:** retry set studio ready call ([#105](https://github.com/botpress/studio/issues/105)) ([8ff62bb](https://github.com/botpress/studio/commit/8ff62bbf21f891cca959d48be2cc3e5c8830f2d7))
* **studio:** Ui changes ([#40](https://github.com/botpress/studio/issues/40)) ([a36be1b](https://github.com/botpress/studio/commit/a36be1b8fa02b6e324e957e1a9a3dd888d28641d))
* **studio-ui:** remove id from bot form parameters ([#90](https://github.com/botpress/studio/issues/90)) ([cffe305](https://github.com/botpress/studio/commit/cffe30525e83b4342344541268ae52fc207b2fcd))
* **ui:** modify channel-vonage content-type warn ([#134](https://github.com/botpress/studio/issues/134)) ([451d556](https://github.com/botpress/studio/commit/451d556b9ee54dca5c0fd9b823e47ee5f4f33f32))
* **ui-shared:** Fix collapsible toggleExpand undef ([#128](https://github.com/botpress/studio/issues/128)) ([9f3148e](https://github.com/botpress/studio/commit/9f3148eaf7e26c113c811986ea9bebf4db97afbb))
* edit translated caroussel data ([#122](https://github.com/botpress/studio/issues/122)) ([feaaafd](https://github.com/botpress/studio/commit/feaaafdcecefcd58810715920772e5f93d1b6f98))


### Features

* **qna:** add RTL content language support ([#132](https://github.com/botpress/studio/issues/132)) ([c8e5d9c](https://github.com/botpress/studio/commit/c8e5d9c0640cf17b7cd82f446903fd40ca029b33))
* Added Chinese language translation ([#135](https://github.com/botpress/studio/issues/135)) ([18221bb](https://github.com/botpress/studio/commit/18221bbf2e72db3fa4607523e70beed1da905e14))
* **dev:** binary branches ([#93](https://github.com/botpress/studio/issues/93)) ([51dd7ce](https://github.com/botpress/studio/commit/51dd7ce679456824f6b909a9d97525133dea49b5))
* improve content rtl support for nodes on botpress studio ([#61](https://github.com/botpress/studio/issues/61)) ([ae36c88](https://github.com/botpress/studio/commit/ae36c88af4fbddc3238529cf4824154714fca933))
* **config:** Add bot id as readonly on bot config ([#64](https://github.com/botpress/studio/issues/64)) ([49cffc5](https://github.com/botpress/studio/commit/49cffc5836cdf0ef065f49ec0d4cb506a84466e4))



## [0.0.38](https://github.com/botpress/studio/compare/v0.0.37...v0.0.38) (2021-09-28)


### Bug Fixes

* edit translated caroussel data ([#122](https://github.com/botpress/studio/issues/122)) ([feaaafd](https://github.com/botpress/studio/commit/feaaafdcecefcd58810715920772e5f93d1b6f98))
* **flow:** fix editing content elements using action modal ([#109](https://github.com/botpress/studio/issues/109)) ([c021286](https://github.com/botpress/studio/commit/c021286a5453c14abe3bc007a432099291afce91))
* **qna:** fix language dropdown overlay issue  ([#115](https://github.com/botpress/studio/issues/115)) ([056686b](https://github.com/botpress/studio/commit/056686b8e71d94723108eb4136fda2e0e335679a))
* **studio:** allow changing conent-type when editing node content-element ([#121](https://github.com/botpress/studio/issues/121)) ([4bf2ae2](https://github.com/botpress/studio/commit/4bf2ae21b7a7aa69520aceade8e9c71189b1c564))
* **studio:** error when state missing in debugger ([#111](https://github.com/botpress/studio/issues/111)) ([31af69f](https://github.com/botpress/studio/commit/31af69fe18b7f2c0e34ca5fd18aaee8a7fcc6f74))
* **studio:** fix permissions for libraries ([#119](https://github.com/botpress/studio/issues/119)) ([86e53ad](https://github.com/botpress/studio/commit/86e53adcff929b6b68ec9089af6b69e61462790b))
* **studio:** prevent creating empty actions ([#118](https://github.com/botpress/studio/issues/118)) ([fed7ac6](https://github.com/botpress/studio/commit/fed7ac66da61b6ac3a5cc0d43ca1106806934e7c))



## [0.0.37](https://github.com/botpress/studio/compare/v0.0.36...v0.0.37) (2021-09-16)


### Bug Fixes

* **studio:** fix session storage with storage utils ([#22](https://github.com/botpress/studio/issues/22)) ([b292015](https://github.com/botpress/studio/commit/b2920157aacc9ef2ac1d386fb7c67656e0c3e78a))
* **studio:** retry set studio ready call ([#105](https://github.com/botpress/studio/issues/105)) ([8ff62bb](https://github.com/botpress/studio/commit/8ff62bbf21f891cca959d48be2cc3e5c8830f2d7))



## [0.0.36](https://github.com/botpress/studio/compare/v0.0.35...v0.0.36) (2021-09-13)


### Bug Fixes

* **flowbuilder:** fix wait on issue with new rtl procedure ([#98](https://github.com/botpress/studio/issues/98)) ([b0f2db4](https://github.com/botpress/studio/commit/b0f2db48bbb533b8beaa14fbe0cb09b9ecaee0f0))



## [0.0.35](https://github.com/botpress/studio/compare/v0.0.34...v0.0.35) (2021-09-10)


### Bug Fixes

* **cms:** display unregistered content-types instead of error message ([#81](https://github.com/botpress/studio/issues/81)) ([d3370be](https://github.com/botpress/studio/commit/d3370be41630760ef5abd2625e6c227c70cff629))
* **dev:** fix condition ([235389d](https://github.com/botpress/studio/commit/235389d3566b2df29c0244b954c9682dd372c1f5))
* **flow:** fix clearing node action parameters on close ([#91](https://github.com/botpress/studio/issues/91)) ([5ffbb9a](https://github.com/botpress/studio/commit/5ffbb9a191a6e1b2e0d702c8729b1746606e1b98))
* **studio:** bot mig same behavior as core ([#86](https://github.com/botpress/studio/issues/86)) ([6fd2bdc](https://github.com/botpress/studio/commit/6fd2bdcfdcab7468d68be7297e393559df4abcba))
* **studio:** overlapping between search bar & node props side panel ([#37](https://github.com/botpress/studio/issues/37)) ([6e4d841](https://github.com/botpress/studio/commit/6e4d841cd83a0ecfb52d1d77d042a7c588b0fcfe))
* **studio-ui:** remove id from bot form parameters ([#90](https://github.com/botpress/studio/issues/90)) ([cffe305](https://github.com/botpress/studio/commit/cffe30525e83b4342344541268ae52fc207b2fcd))


### Features

* **dev:** binary branches ([#93](https://github.com/botpress/studio/issues/93)) ([51dd7ce](https://github.com/botpress/studio/commit/51dd7ce679456824f6b909a9d97525133dea49b5))



## [0.0.34](https://github.com/botpress/studio/compare/v0.0.33...v0.0.34) (2021-09-02)


### Bug Fixes

* **core:** fix redis pubsub by adding redis scopes ([#83](https://github.com/botpress/studio/issues/83)) ([2ed671f](https://github.com/botpress/studio/commit/2ed671fef6c6f0f6cca6c58a7df193ce9985286f))
* **debugger:** use message ids ([#30](https://github.com/botpress/studio/issues/30)) ([2fc6756](https://github.com/botpress/studio/commit/2fc67569d72188a6776805d0e9e1bc21066fb958))
* **studio:** fix typings for yn signature ([42c11e0](https://github.com/botpress/studio/commit/42c11e0c988e817384a8dda9f0a95a55b035b34a))


### Features

* improve content rtl support for nodes on botpress studio ([#61](https://github.com/botpress/studio/issues/61)) ([ae36c88](https://github.com/botpress/studio/commit/ae36c88af4fbddc3238529cf4824154714fca933))



## [0.0.33](https://github.com/botpress/studio/compare/v0.0.32...v0.0.33) (2021-08-20)


### Features

* **config:** Add bot id as readonly on bot config ([#64](https://github.com/botpress/studio/issues/64)) ([49cffc5](https://github.com/botpress/studio/commit/49cffc5836cdf0ef065f49ec0d4cb506a84466e4))



## [0.0.32](https://github.com/botpress/studio/compare/v0.0.31...v0.0.32) (2021-08-19)


### Bug Fixes

* **libraries:** fix inexistent folder ([74f8c01](https://github.com/botpress/studio/commit/74f8c016b83e71e2e59e78f37aaf46da8a856d91))



## [0.0.31](https://github.com/botpress/studio/compare/v0.0.30...v0.0.31) (2021-08-19)


### Bug Fixes

* **libraries:** more fix for distributed usage ([c22d9de](https://github.com/botpress/studio/commit/c22d9de9ebe9819e434abec837e1b53b244ad97a))



## [0.0.30](https://github.com/botpress/studio/compare/v0.0.29...v0.0.30) (2021-08-19)


### Bug Fixes

* **libraries:** updated logic to use disk first ([#63](https://github.com/botpress/studio/issues/63)) ([84ec937](https://github.com/botpress/studio/commit/84ec9378f0a60f5f2e2f9b6822facf16d42446a5))



## [0.0.29](https://github.com/botpress/studio/compare/v0.0.28...v0.0.29) (2021-08-19)


### Bug Fixes

* **studio:** fix delete nodes link issue ([#62](https://github.com/botpress/studio/issues/62)) ([52ff16e](https://github.com/botpress/studio/commit/52ff16e970504106ee085e07285baf4abd9b401f))
* **studio:** move events-related routes ([#56](https://github.com/botpress/studio/issues/56)) ([ccf010f](https://github.com/botpress/studio/commit/ccf010f2b81e2e6aca8ee5b876e84d5f1bde0e2a))
* **studio:** Ui changes ([#40](https://github.com/botpress/studio/issues/40)) ([a36be1b](https://github.com/botpress/studio/commit/a36be1b8fa02b6e324e957e1a9a3dd888d28641d))



## [0.0.28](https://github.com/botpress/studio/compare/v0.0.27...v0.0.28) (2021-08-18)


### Bug Fixes

* **studio:** guided tour highlighting restored and more ([#44](https://github.com/botpress/studio/issues/44)) ([5793df7](https://github.com/botpress/studio/commit/5793df7601dad375b626fa57d42eff1119038f8a))


### Features

* **studio:** add bot-scoped libraries ([#34](https://github.com/botpress/studio/issues/34)) ([5b20b23](https://github.com/botpress/studio/commit/5b20b23a3371eba0b7817c4b1884a58279c17b4d))



## [0.0.27](https://github.com/botpress/studio/compare/v0.0.26...v0.0.27) (2021-08-17)


### Bug Fixes

* Swapped flow and content menu items in side menu ([#54](https://github.com/botpress/studio/issues/54)) ([113cf35](https://github.com/botpress/studio/commit/113cf3559c1edfa998b094fd67cea0fa0283b451))


### Features

* **studio:** implement bot migration ([#31](https://github.com/botpress/studio/issues/31)) ([d82f9c2](https://github.com/botpress/studio/commit/d82f9c2c835e6b5da9b87877ae598a744eae6295))



## [0.0.26](https://github.com/botpress/studio/compare/v0.0.25...v0.0.26) (2021-08-11)


### Features

* **qna:** extract module qna on studio ([#29](https://github.com/botpress/studio/issues/29)) ([7ed09fa](https://github.com/botpress/studio/commit/7ed09fa383d6b7fbcf171f9ee0f38fc8aa93fc3f))



## [0.0.25](https://github.com/botpress/studio/compare/v0.0.24...v0.0.25) (2021-08-11)


### Bug Fixes

* **studio:** don't open node props when moving it ([#35](https://github.com/botpress/studio/issues/35)) ([7dd773d](https://github.com/botpress/studio/commit/7dd773d4a9b14783c86c414e39292ad3b9e7a25e))
* **studio:** UI toast modifications ([#27](https://github.com/botpress/studio/issues/27)) ([53feee2](https://github.com/botpress/studio/commit/53feee2761917bdc182007cc2f9adc5a277bb087))


### Features

* **studio:** Custom Icon for Conversation End Flows ([#36](https://github.com/botpress/studio/issues/36)) ([1e4350c](https://github.com/botpress/studio/commit/1e4350cd99a0e1f27f5a208cefd4d76c7dd96e44))



## [0.0.24](https://github.com/botpress/studio/compare/v0.0.23...v0.0.24) (2021-07-30)


### Bug Fixes

* **core:** fix disk storage race condition ([#28](https://github.com/botpress/studio/issues/28)) ([4bc8455](https://github.com/botpress/studio/commit/4bc8455487e7a6e312166aa54bb6611474f85fe6))



## [0.0.23](https://github.com/botpress/studio/compare/v0.0.22...v0.0.23) (2021-07-13)


### Bug Fixes

* **config:** cannot save config ([d5354cc](https://github.com/botpress/studio/commit/d5354ccd5b2c821f49364df74c21358c1e4a664e))



## [0.0.22](https://github.com/botpress/studio/compare/v0.0.21...v0.0.22) (2021-06-28)


### Bug Fixes

* make sure each contentItem is matching it's id ([#25](https://github.com/botpress/studio/issues/25)) ([275c0df](https://github.com/botpress/studio/commit/275c0df5c1c1199f9eeec37862cc5fca1832fc6e))


### Features

* **studio:** add support for help/hints with content form ([4c6691a](https://github.com/botpress/studio/commit/4c6691ab3c147625f83d62a554217e1e476df29d))
* **studio:** add support for help/hints with content form ([75f5db7](https://github.com/botpress/studio/commit/75f5db76fc56b86738572faf5194020042082679))



## [0.0.21](https://github.com/botpress/studio/compare/v0.0.20...v0.0.21) (2021-06-15)



## [0.0.20](https://github.com/botpress/studio/compare/v0.0.19...v0.0.20) (2021-06-10)


### Bug Fixes

* **core:** fix module paths ([#17](https://github.com/botpress/studio/issues/17)) ([0e4d397](https://github.com/botpress/studio/commit/0e4d3970c6c19e09dde19ccc20fff5b307e21403))



## [0.0.19](https://github.com/botpress/studio/compare/v0.0.18...v0.0.19) (2021-06-09)


### Bug Fixes

* **studio:** add db logger ([#16](https://github.com/botpress/studio/issues/16)) ([029f574](https://github.com/botpress/studio/commit/029f574965e847998ef5a55c18e3c74ae26f8ad7))



## [0.0.18](https://github.com/botpress/studio/compare/v0.0.16...v0.0.18) (2021-06-09)



## [0.0.16](https://github.com/botpress/studio/compare/v0.0.15...v0.0.16) (2021-06-03)


### Bug Fixes

* **core:** issue with cms on cluster ([14b9fff](https://github.com/botpress/studio/commit/14b9fff74dc4640b1b992f015391729c7b927870))



## [0.0.15](https://github.com/botpress/studio/compare/v0.0.14...v0.0.15) (2021-06-03)



## [0.0.14](https://github.com/botpress/studio/compare/v0.0.13...v0.0.14) (2021-06-03)



## [0.0.13](https://github.com/botpress/studio/compare/v0.0.12...v0.0.13) (2021-06-03)


### Bug Fixes

* **core:** warn parent process of readiness ([1836d81](https://github.com/botpress/studio/commit/1836d811fcc73fe95d66650edb950bb49fd4d2af))
* **studio:** url is less visible in logs ([#11](https://github.com/botpress/studio/issues/11)) ([bc5706a](https://github.com/botpress/studio/commit/bc5706a2cfa6ee0ab4697e7ece42f59085ca17f7))



## [0.0.12](https://github.com/botpress/studio/compare/v0.0.11...v0.0.12) (2021-06-02)


### Bug Fixes

* **build:** fix build again ([#9](https://github.com/botpress/studio/issues/9)) ([ffc36af](https://github.com/botpress/studio/commit/ffc36af9fc735c6b24ff0246de2f56d63feb20e1))



## [0.0.11](https://github.com/botpress/studio/compare/v0.0.10...v0.0.11) (2021-06-02)


### Bug Fixes

* **build:** fix packaging ([#8](https://github.com/botpress/studio/issues/8)) ([994ad1f](https://github.com/botpress/studio/commit/994ad1f1dc72927cd554afe9c5c7bd4f3fac475c))



## [0.0.10](https://github.com/botpress/studio/compare/v0.0.9...v0.0.10) (2021-06-01)


### Bug Fixes

* **build:** missing native ext ([#6](https://github.com/botpress/studio/issues/6)) ([971f201](https://github.com/botpress/studio/commit/971f201399d92cf7147d8ef62894c32b43d6cd70))



## [0.0.9](https://github.com/botpress/studio/compare/v0.0.8...v0.0.9) (2021-06-01)


### Bug Fixes

* **dx:** fix changelog defaults ([2de4d51](https://github.com/botpress/studio/commit/2de4d5143582d2bf3c985aac4bacd2ec47cb3c09))
* **dx:** fix changelog defaults ([4a14377](https://github.com/botpress/studio/commit/4a1437776cea9030866d2b6fe0da6042cdc11159))
* **studio:** fix job service and invalidations ([dea25ba](https://github.com/botpress/studio/commit/dea25ba68e6bbea9c0b6f6fa0295e1f8ca8ba0d5))
* **studio:** update version ([#4](https://github.com/botpress/studio/issues/4)) ([36450f6](https://github.com/botpress/studio/commit/36450f6742a53c7fda2d10539a171a7735e64abc))



## [0.0.8](https://github.com/botpress/studio/compare/v0.0.7...v0.0.8) (2021-05-27)



## [0.0.7](https://github.com/botpress/studio/compare/v0.0.6...v0.0.7) (2021-05-26)


### Bug Fixes

* **studio:** disk invalidations must be from core ([33c207b](https://github.com/botpress/studio/commit/33c207b4749a27c09990d404680f06a4c64aed68))



## [0.0.6](https://github.com/botpress/studio/compare/v0.0.5...v0.0.6) (2021-05-26)



## [0.0.5](https://github.com/botpress/studio/compare/v0.0.4...v0.0.5) (2021-05-26)



## [0.0.4](https://github.com/botpress/studio/compare/v0.0.3...v0.0.4) (2021-05-26)



## [0.0.3](https://github.com/botpress/studio/compare/v0.0.2...v0.0.3) (2021-05-26)



## [0.0.2](https://github.com/botpress/studio/compare/v0.0.1...v0.0.2) (2021-05-25)



## [0.0.1](https://github.com/botpress/studio/compare/0.0.1...v0.0.1) (2021-05-21)



## 0.0.1 (2021-05-20)



## [0.0.33](https://github.com/botpress/studio/compare/v0.0.32...v0.0.33) (2021-08-20)


### Features

* **config:** Add bot id as readonly on bot config ([49cffc5](https://github.com/botpress/studio/commit/49cffc5836cdf0ef065f49ec0d4cb506a84466e4))



## [0.0.31](https://github.com/botpress/studio/compare/v0.0.30...v0.0.31) (2021-08-19)


### Bug Fixes

* **libraries:** updated logic to use disk first ([f42a3eb](https://github.com/botpress/studio/commit/f42a3eb67f512aab181df598ed832b810b7f9faf))



## [0.0.26](https://github.com/botpress/studio/compare/v0.0.23...v0.0.26) (2021-08-16)


### Features

* **studio:** implement bot migration ([89a3a0a](https://github.com/botpress/studio/commit/89a3a0ae6a56484bfcffe3e744f1b4405b62ab1f))



## [0.0.21](https://github.com/botpress/studio/compare/v0.0.20...v0.0.21) (2021-06-16)



## [0.0.17](https://github.com/botpress/studio/compare/v0.0.16...v0.0.17) (2021-06-09)



## [0.0.13](https://github.com/botpress/studio/compare/v0.0.12...v0.0.13) (2021-06-03)

### Bug Fixes

- **core:** warn parent process of readiness ([1836d81](https://github.com/botpress/studio/commit/1836d811fcc73fe95d66650edb950bb49fd4d2af))

## [0.0.9](https://github.com/botpress/studio/compare/v0.0.8...v0.0.9) (2021-06-01)

### Bug Fixes

- **dx:** fix changelog defaults ([4a14377](https://github.com/botpress/studio/commit/4a1437776cea9030866d2b6fe0da6042cdc11159))
- **studio:** fix job service and invalidations ([dea25ba](https://github.com/botpress/studio/commit/dea25ba68e6bbea9c0b6f6fa0295e1f8ca8ba0d5))
