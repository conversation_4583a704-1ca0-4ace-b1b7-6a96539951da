// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/mixins";
@import "../../common/variables";
@import "../progress-bar/common";

$button-border-width: 1px !default;
$button-padding: ($pt-grid-size / 2) $pt-grid-size !default;
$button-padding-small: 0 ($pt-grid-size * 0.7) !default;
$button-padding-large: ($pt-grid-size / 2) ($pt-grid-size * 1.5) !default;
$button-icon-spacing: ($pt-button-height - $pt-icon-size-standard) / 2 !default;
$button-icon-spacing-large: ($pt-button-height-large - $pt-icon-size-large) / 2 !default;

/*
CSS `border` property issues:
- An element can only have one border.
- Borders can't stack with shadows.
- Borders modify the size of the element they're applied to.
- Border positioning requires the extra `box-sizing` property.

`box-shadow` doesn't have these issues, we're using it instead of `border`.
*/
$button-box-shadow:
  inset 0 0 0 $button-border-width rgba($black, 0.2),
  inset 0 (-$button-border-width) 0 rgba($black, 0.1) !default;
$button-box-shadow-active:
  inset 0 0 0 $button-border-width rgba($black, 0.2),
  inset 0 1px 2px rgba($black, 0.2) !default;
$button-intent-box-shadow:
  inset 0 0 0 $button-border-width rgba($black, 0.4),
  inset 0 (-$button-border-width) 0 rgba($black, 0.2) !default;
$button-intent-box-shadow-active:
  inset 0 0 0 $button-border-width rgba($black, 0.4),
  inset 0 1px 2px rgba($black, 0.2) !default;

/*
Overlay shadows are used for default buttons
floating on top of other elements. This way, the
shadows blend with the colors beneath it.
Switches and slider handles both use these variables.
*/
$button-box-shadow-overlay:
  0 0 0 $button-border-width rgba($black, 0.2),
  0 1px 1px rgba($black, 0.2) !default;
$button-box-shadow-overlay-active:
  0 0 0 $button-border-width rgba($black, 0.2),
  inset 0 1px 1px rgba($black, 0.1) !default;

$dark-button-box-shadow:
  0 0 0 $button-border-width rgba($black, 0.4) !default;
$dark-button-box-shadow-active:
  0 0 0 $button-border-width rgba($black, 0.6),
  inset 0 1px 2px rgba($black, 0.2) !default;
$dark-button-intent-box-shadow:
  0 0 0 $button-border-width rgba($black, 0.4) !default;
$dark-button-intent-box-shadow-active:
  0 0 0 $button-border-width rgba($black, 0.4),
  inset 0 1px 2px rgba($black, 0.2) !default;

$button-gradient: linear-gradient(to bottom, rgba($white, 0.8), rgba($white, 0)) !default;
$button-intent-gradient: linear-gradient(to bottom, rgba($white, 0.1), rgba($white, 0)) !default;
$dark-button-gradient: linear-gradient(to bottom, rgba($white, 0.05), rgba($white, 0)) !default;

$button-color-disabled: $pt-text-color-disabled !default;
$button-background-color: $light-gray5 !default;
$button-background-color-hover: $light-gray4 !default;
$button-background-color-active: $light-gray2 !default;
$button-background-color-disabled: rgba($light-gray1, 0.5) !default;
$button-background-color-active-disabled: rgba($light-gray1, 0.7) !default;
$button-intent-color-disabled: rgba($white, 0.6);
$dark-button-color-disabled: $pt-dark-text-color-disabled !default;
$dark-button-background-color: $dark-gray5 !default;
$dark-button-background-color-hover: $dark-gray4 !default;
$dark-button-background-color-active: $dark-gray2 !default;
$dark-button-background-color-disabled: rgba($dark-gray5, 0.5) !default;
$dark-button-background-color-active-disabled: rgba($dark-gray5, 0.7) !default;
$dark-button-intent-color-disabled: rgba($white, 0.3);

$minimal-button-divider-width: 1px !default;
$minimal-button-background-color: none !default;
$minimal-button-background-color-hover: rgba($gray4, 0.3) !default;
$minimal-button-background-color-active: rgba($gray2, 0.3) !default;
$dark-minimal-button-background-color: none !default;
$dark-minimal-button-background-color-hover: rgba($gray3, 0.15) !default;
$dark-minimal-button-background-color-active: rgba($gray3, 0.3) !default;

$button-outlined-width: 1px !default;
$button-outlined-border-intent-opacity: 0.6 !default;
$button-outlined-border-disabled-intent-opacity: 0.2 !default;

// "intent": (default, hover, active colors)
$button-intents: (
  "primary": ($pt-intent-primary, $blue2, $blue1),
  "success": ($pt-intent-success, $green2, $green1),
  "warning": ($pt-intent-warning, $orange2, $orange1),
  "danger": ($pt-intent-danger, $red2, $red1)
) !default;

@mixin pt-button-base() {
  @include pt-flex-container(row, $button-icon-spacing, $inline: inline);
  align-items: center;
  justify-content: center;

  border: none;
  border-radius: $pt-border-radius;
  cursor: pointer;
  padding: $button-padding;
  vertical-align: middle;
  text-align: left;
  font-size: $pt-font-size;
}

@mixin pt-button-height($height) {
  min-width: $height;
  min-height: $height;
}

@mixin pt-button-height-large() {
  @include pt-button-height($pt-button-height-large);
  @include pt-flex-margin(row, $button-icon-spacing-large);
  padding: $button-padding-large;
  font-size: $pt-font-size-large;
}

@mixin pt-button-height-default() {
  @include pt-button-height($pt-button-height);
  padding: $button-padding;
}

@mixin pt-button-height-small() {
  @include pt-button-height($pt-button-height-small);
  padding: $button-padding-small;
}

@mixin pt-button() {
  box-shadow: $button-box-shadow;
  background-color: $button-background-color;
  background-image: $button-gradient;
  color: $pt-text-color;

  &:hover {
    @include pt-button-hover();
  }

  &:active,
  &.#{$ns}-active {
    @include pt-button-active();
  }

  &:disabled,
  &.#{$ns}-disabled {
    @include pt-button-disabled();
  }
}

@mixin pt-button-hover() {
  box-shadow: $button-box-shadow;
  background-clip: padding-box;
  background-color: $button-background-color-hover;
}

@mixin pt-button-active() {
  box-shadow: $button-box-shadow-active;
  background-color: $button-background-color-active;
  background-image: none;
}

@mixin pt-button-disabled() {
  outline: none;
  box-shadow: none;
  background-color: $button-background-color-disabled;
  background-image: none;
  cursor: not-allowed;
  color: $button-color-disabled;

  &.#{$ns}-active,
  &.#{$ns}-active:hover {
    background: $button-background-color-active-disabled;
  }
}

@mixin pt-button-intent($default-color, $hover-color, $active-color) {
  box-shadow: $button-intent-box-shadow;
  background-color: $default-color;
  background-image: $button-intent-gradient;
  color: $white;

  &:hover,
  &:active,
  &.#{$ns}-active {
    color: $white;
  }

  &:hover {
    box-shadow: $button-intent-box-shadow;
    background-color: $hover-color;
  }

  &:active,
  &.#{$ns}-active {
    box-shadow: $button-intent-box-shadow-active;
    background-color: $active-color;
    background-image: none;
  }

  &:disabled,
  &.#{$ns}-disabled {
    @include pt-button-intent-disabled($default-color);
  }
}

@mixin pt-button-intent-disabled($default-color) {
  border-color: transparent;
  box-shadow: none;
  background-color: rgba($default-color, 0.5);
  background-image: none;
  color: $button-intent-color-disabled;
}

@mixin pt-dark-button() {
  box-shadow: $dark-button-box-shadow;
  background-color: $dark-button-background-color;
  background-image: $dark-button-gradient;
  color: $pt-dark-text-color;

  &:hover,
  &:active,
  &.#{$ns}-active {
    color: $pt-dark-text-color;
  }

  &:hover {
    @include pt-dark-button-hover();
  }

  &:active,
  &.#{$ns}-active {
    @include pt-dark-button-active();
  }

  &:disabled,
  &.#{$ns}-disabled {
    @include pt-dark-button-disabled();
  }

  .#{$ns}-button-spinner .#{$ns}-spinner-head {
    background: $dark-progress-track-color;
    stroke: $dark-progress-head-color;
  }
}

@mixin pt-dark-button-hover() {
  box-shadow: $dark-button-box-shadow;
  background-color: $dark-button-background-color-hover;
}

@mixin pt-dark-button-active() {
  box-shadow: $dark-button-box-shadow-active;
  background-color: $dark-button-background-color-active;
  background-image: none;
}

@mixin pt-dark-button-disabled() {
  box-shadow: none;
  background-color: $dark-button-background-color-disabled;
  background-image: none;
  color: $dark-button-color-disabled;

  &.#{$ns}-active {
    background: $dark-button-background-color-active-disabled;
  }
}

@mixin pt-dark-button-intent() {
  box-shadow: $dark-button-intent-box-shadow;

  &:hover {
    box-shadow: $dark-button-intent-box-shadow;
  }

  &:active,
  &.#{$ns}-active {
    box-shadow: $dark-button-intent-box-shadow-active;
  }

  &:disabled,
  &.#{$ns}-disabled {
    @include pt-dark-button-intent-disabled();
  }
}

@mixin pt-dark-button-intent-disabled() {
  box-shadow: none;
  background-image: none;
  color: $dark-button-intent-color-disabled;
}

@mixin pt-button-minimal() {
  box-shadow: none;
  background: $minimal-button-background-color;

  &:hover {
    box-shadow: none;
    background: $minimal-button-background-color-hover;
    text-decoration: none;
    color: $pt-text-color;
  }

  &:active,
  &.#{$ns}-active {
    box-shadow: none;
    background: $minimal-button-background-color-active;
    color: $pt-text-color;
  }

  &:disabled,
  &:disabled:hover,
  &.#{$ns}-disabled,
  &.#{$ns}-disabled:hover {
    background: none;
    cursor: not-allowed;
    color: $pt-text-color-disabled;

    &.#{$ns}-active {
      background: $minimal-button-background-color-active;
    }
  }

  .#{$ns}-dark & {
    @include pt-dark-button-minimal();
  }

  @each $intent, $colors in $button-intents {
    &.#{$ns}-intent-#{$intent} {
      @include pt-button-minimal-intent(
        map-get($pt-intent-colors, $intent),
        map-get($pt-intent-text-colors, $intent),
        map-get($pt-dark-intent-text-colors, $intent)
      );
    }
  }
}

@mixin pt-dark-button-minimal() {
  box-shadow: none;
  background: $dark-minimal-button-background-color;
  color: inherit;

  &:hover,
  &:active,
  &.#{$ns}-active {
    box-shadow: none;
    background: none;
  }

  &:hover {
    background: $dark-minimal-button-background-color-hover;
  }

  &:active,
  &.#{$ns}-active {
    background: $dark-minimal-button-background-color-active;
    color: $pt-dark-text-color;
  }

  &:disabled,
  &:disabled:hover,
  &.#{$ns}-disabled,
  &.#{$ns}-disabled:hover {
    background: none;
    cursor: not-allowed;
    color: $pt-dark-text-color-disabled;

    &.#{$ns}-active {
      background: $dark-minimal-button-background-color-active;
    }
  }
}

@mixin pt-button-minimal-intent($intent-color, $text-color, $dark-text-color) {
  color: $text-color;

  &:hover,
  &:active,
  &.#{$ns}-active {
    box-shadow: none;
    background: none;
    color: $text-color;
  }

  &:hover {
    background: rgba($intent-color, 0.15);
    color: $text-color;
  }

  &:active,
  &.#{$ns}-active {
    background: rgba($intent-color, 0.3);
    color: $text-color;
  }

  &:disabled,
  &.#{$ns}-disabled {
    background: none;
    color: rgba($text-color, 0.5);

    &.#{$ns}-active {
      background: rgba($intent-color, 0.3);
    }
  }

  .#{$ns}-button-spinner .#{$ns}-spinner-head {
    stroke: $text-color;
  }

  .#{$ns}-dark & {
    color: $dark-text-color;

    &:hover {
      background: rgba($intent-color, 0.2);
      color: $dark-text-color;
    }

    &:active,
    &.#{$ns}-active {
      background: rgba($intent-color, 0.3);
      color: $dark-text-color;
    }

    &:disabled,
    &.#{$ns}-disabled {
      background: none;
      color: rgba($dark-text-color, 0.5);

      &.#{$ns}-active {
        background: rgba($intent-color, 0.3);
      }
    }
  }
}

@mixin pt-button-minimal-divider() {
  $divider-height: $pt-grid-size * 2;

  margin: ($pt-button-height - $divider-height) / 2;
  background: $pt-divider-black;
  width: $minimal-button-divider-width;

  .#{$ns}-dark & {
    background: $pt-dark-divider-white;
  }
}

@mixin pt-button-outlined() {
  border: $button-outlined-width solid rgba($pt-text-color, 0.2);
  box-sizing: border-box;

  &:disabled,
  &.#{$ns}-disabled,
  &:disabled:hover,
  &.#{$ns}-disabled:hover {
    border-color: rgba($pt-text-color-disabled, 0.1);
  }

  .#{$ns}-dark & {
    @include pt-dark-button-outlined();
  }

  @each $intent, $colors in $button-intents {
    &.#{$ns}-intent-#{$intent} {
      @include pt-button-outlined-intent(
        map-get($pt-intent-text-colors, $intent),
        map-get($pt-dark-intent-text-colors, $intent)
      );
    }
  }
}

@mixin pt-dark-button-outlined() {
  border-color: rgba($white, 0.4);

  &:disabled,
  &:disabled:hover,
  &.#{$ns}-disabled,
  &.#{$ns}-disabled:hover {
    border-color: rgba($white, 0.2);
  }
}

@mixin pt-button-outlined-intent($text-color, $dark-text-color) {
  border-color: rgba($text-color, $button-outlined-border-intent-opacity);

  &:disabled,
  &.#{$ns}-disabled {
    border-color: rgba($text-color, $button-outlined-border-disabled-intent-opacity);
  }

  .#{$ns}-dark & {
    border-color: rgba($dark-text-color, $button-outlined-border-intent-opacity);

    &:disabled,
    &.#{$ns}-disabled {
      border-color: rgba($dark-text-color, $button-outlined-border-disabled-intent-opacity);
    }
  }
}
