{"config": {"additionalDetails": "Additional Details", "avatarAndCover": "Avatar & Cover picture", "avatarUploadSuccess": "The bot avatar has been uploaded successfully. You need to save the form in order for the changes to take effect.", "botAvatar": "<PERSON><PERSON>", "botConfiguration": "Bot Configuration", "chooseFile": "Choose file", "configUpdated": "Bot configuration updated successfully", "confirmChangeLanguage": "Are you sure you want to change the language of your bot from {currentName} to {newName}? All of your content elements will be copied, make sure you translate them.", "confirmUnmount": "Are you sure want to unmount this bot? All of the functionalities of this bot will become unavailable.", "contactEmail": "Contact E-mail", "coverPicture": "Cover Picture", "coverUploadSuccess": "The cover picture has been uploaded successfully. You need to save the form in order for the changes to take effect.", "defaultLanguage": "Default language", "description": "Description", "formContainsErrors": "The form contains errors", "language": "Language", "linkToPolicy": "Link to Privacy Policy", "linkToTerms": "Link to Terms & Conditions", "phoneNumber": "Phone Number", "requireImageFile": "{targetProp} requires an image file", "supportedLanguages": "Supported languages", "website": "Website", "botId": "Bot Id", "copyToClipboard": "Copied to clipboard"}, "libraries": {"fullName": "Libraries", "addLibrary": "Add this library", "searchNpm": "Search library on NPM (recommended)", "searchGithub": "Add from a GitHub repository (can be slow)", "uploadArchive": "Upload an archive", "splash": {"text1": "These libraries can be used for this bot's actions and hooks. You can also create custom pieces of code which can be imported by hooks and actions on the code editor. They will be exported in the generated bot archive.", "text2": "When editing the file 'package.json' manually using the code editor, please click the 'Sync' button below to update dependencies"}, "sync": "Synchronize", "syncSuccess": "Sync completed successfully", "viewGithub": "View on Github", "customCommand": "Type a custom command", "openConsole": "Open the console to view command output", "bundleDeps": "Package with dependencies", "addSuccess": "Library added successfully!", "libraryName": "Library name", "confirmRemove": "Are you sure you want to remove this library?", "deleteSuccess": "Library removed successfully", "removeFailure": "There was an error while removing the library. Please check server logs for details", "bundleError": "There was an error while packaging the library", "betaWarning": "This is an experimental feature. Please do not rely on it too much because the implementation may change in the near future"}, "nlu": {"autoTrain": "Auto Training", "cancelTraining": "Cancel Training", "description": "Tap + in the sidebar to define your first intent.", "entities": {"deleteMessage": "Are you sure you want to delete the entity \"{entityName}\" ?", "duplicate": "Duplicate Entity", "examplesLabel": "Matching examples", "examplesPlaceholder": "Add examples that match your pattern (one per line)", "filterPlaceholder": "Filter entities", "fuzzyLabel": "Fuzzy matching options", "fuzzyTooltip": "Fuzziness will tolerate slight errors (e.g: typos) in words of 4 characters or more. Strict means no errors allowed.", "loose": "Loose", "matchCaseLabel": "Match case", "matchCaseTooltip": "Whether your pattern is case sensitive", "matchingError": "Some examples don't match", "matchingSuccess": "All examples match", "medium": "Medium", "nameConflictMessage": "An entity with that name already exists. Please choose another one.", "nameConflictTitle": "Name already in use", "namePlaceholder": "Entity name", "new": "New entity", "newOccurrence": "New occurrence", "occurrenceLabel": "Occurrences", "occurrencePlaceholder": "Type a value (ex: Chicago)", "occurrenceTooltip": "An occurrence is a value of your entity. Each occurrence can have multiple synonyms.", "patternInvalid": "Invalid pattern", "patternLabel": "Regular expression", "patternPlaceholder": "Insert a valid pattern", "rename": "<PERSON><PERSON>", "selectPlaceholder": "Select entities...", "sensitiveLabel": "Contains sensitive data", "sensitiveTooltip": "Sensitive information are replaced by * before being saved in the database", "strict": "Strict", "synonymPlaceholder": "Type a synonym (or more, comma separated) and hit enter", "title": "Entities"}, "intents": {"actionErrorMessage": "Could not {action} intent", "chooseContainerLabel": "Choose a different intent for the condition", "contextSelectorCreateMissing": "Create \"{query}\"", "contextSelectorPlaceholder": "Select context...", "contextSelectorTooltip": "You can type in the select bar to add new contexts.", "createLabel": "Create intent", "deleteConfirmMessage": "Are you sure you want to delete the intent \"{intentName}\"?", "exactOnly": "exact match only", "filterPlaceholder": "Filter intents", "hintExactMatch": "This intent will use {exactOnly}. To enable machine learning, add at least {nb} more utterances.", "hintIgnored": "This intent will be ignored, start adding utterances to make it trainable.", "hintResilient": "Add {nb} more utterances to make NLU more resilient.", "nameDupe": "An intent with that name already exists. Please choose another one.", "nameLabel": "Intent Name", "namePlaceholder": "Choose a name for your intent", "new": "New intent", "selectIntentLabel": "Select an existing intent", "selectIntentNoResults": "No such intent", "summaryPlaceholder": "Summary of intent", "title": "Intents", "utterancePlaceholder": "Type a sentence"}, "slots": {"createTitle": "Create a slot for your intent", "deleteMessage": "Are you sure you want to delete this slot and all associated tagging from all utterances?", "editTitle": "Edit Slot", "emptyState": "No slots defined for this intent", "entitiesLabel": "Associated Entities", "nameLabel": "Slot name", "namePlaceholder": "Type a name here", "new": "Create slot", "noSlotsToTag": "Selection can't be tagged. Define a slot first.", "save": "Save slot", "tagSelectionLabel": "Tag selection", "tagSelectionMessage": "Click on a slot or use numbers as keyboard shortcuts", "names": {"amountOfMoney": "amountOfMoney", "distance": "distance", "duration": "duration", "email": "email", "number": "number", "ordinal": "ordinal", "phoneNumber": "phoneNumber", "quantity": "quantity", "temperature": "temperature", "time": "time", "url": "url", "volume": "volume", "any": "any"}}, "title": "Language Understanding", "trainNow": "Train now"}, "qna": {"addNew": "Add new", "answer": "Answer", "confirmDelete": "Do you want to delete the question?", "context": {"canTypeToCreate": "You can type in the select bar to add new contexts.", "createQuery": "Create context", "filterByContexts": "Search by context", "selectContext": "Select context...", "title": "Contexts"}, "contexts": "Contexts", "create": "Create a new Q&A", "edit": "Edit Q&A", "editor": {"andOr": "and / or", "answers": "Answers", "botWillSay": "<PERSON><PERSON> will say: ", "checkboxRequired": "Action checkbox is required", "duplicatesNotAllowed": "Duplicated questions aren't allowed.", "incorrectRedirection": "Incorrect redirection", "inputsRequred": "Inputs are required.", "missingTranslations": "Missing translations", "node": "Node", "pasteQuestionHere": "Type/Paste your questions here separated with a new line", "questions": "Questions", "redirectToFlow": "Redirect to flow", "typePressAddAnswer": "Type and press enter to add an answer. Use ALT+Enter for a new line"}, "exportToJson": "Export to JSON", "form": {"a": "A", "addAnswerAlternative": "Add Answer Alternatives", "addContent": "Add Content", "addMoreQuestionsPlural": "Add {count} more questions to make your Q&A more resilient", "addMoreQuestionsSingular": "Add 1 more question to make your Q&A more resilient", "addOneItemTooltip": "Add one item first", "addQuestion": "Add Question", "addQuestionAlternative": "Add Question Alternatives", "cantBeSaved": "Can't be saved", "chatbotWillRandomlyChoose": "<PERSON><PERSON><PERSON> will answer randomly from these alternatives", "confirmDeleteQuestion": "Are you sure you want to delete this question? Question and answer alternatives will be deleted as well.", "confirmConvertToIntent": "Are you sure you want to convert this question to intent? Answers alternatives will be deleted.", "copyIdToClipboard": "Copy ID to clipboard", "deleteQuestion": "Delete Question", "disabledTooltip": "This Q&A will not be displayed to users.", "disableQuestion": "Disable Question", "disableRedirection": "Disable Redirection", "duplicateAnswer": "You have already written this answer", "duplicateQuestion": "You have already written this question", "emptyState": "Tap + in the toolbar to add your first question.", "enableQuestion": "Enable Question", "enableRedirection": "Enable Redirection", "convertToIntent": "Convert To Intent", "idCopiedToClipboard": "ID copied to clipboard", "incomplete": "Incomplete", "incompleteTooltip": "This Q&A will use exact match only.", "missingAnswer": "Leaving answer field empty will disable this question.", "missingQuestion": "Leaving question field empty will disable this question.", "node": "Node", "noResultsFromFilters": "No questions found", "onlyOneLanguage": "You only have one language", "pickNode": "Pick a Node", "pickWorkflow": "Pick a Workflow", "q": "Q", "quickAddAlternative": "Press {shortcut} while inline to add new alternative quickly", "redirectQuestionTo": "Redirect question to", "redirectToWorkflow": "Redirect to Workflow", "translate": "Translate", "workflow": "Workflow", "writeAtLeastTwoMoreQuestions": "Write at least 2 question alternatives to enable machine learning", "writeFirstQuestion": "Write a sentence that your user could write to ask his/her question", "writeTheAnswer": "Write the answer to the question", "writingSameQuestion": "Writing the same question twice will disable this Q&A."}, "fullName": "Q&A", "hint": {"addMoreQuestions": "Add {moreQuestions} to make your Q&A more resilient.", "exactMatchOnly": "exact match only", "moreQuestions": "{remaining} more {remaining, plural, one {question} other {questions}}", "willBeExact": "This Q&A will use {exactMatchOnly}. To enable machine learning, add at leastd {remaining} more {remaining, plural, one {question} other {questions}}", "willBeIgnored": "This Q&A will be ignored, start adding questions to make it trainable."}, "import": {"analysis": "Analysis", "botContains": "The bot contains {qnaCount} questions and {cmsCount} content elements.", "clearQuestionsThenInsert": "Clear existing questions, then insert my new questions and create/update content elements", "clearQuestionsAnalyticsWarning": "Clearing questions will make them unavailable in the analytics report", "fileContains": "Your file contains {fileQnaCount} questions and {fileCmsCount} content elements.", "insertNewQuestions": "Insert the new questions from my file and create/update associated content elements", "notAbleToExtract": "We were not able to extract any data from your file. Either the file is empty, or it doesn't match any known format.", "selectJson": "Select your JSON file", "selectJsonHelp": "Select a JSON file exported from the module QNA. You will see a summary of modifications when clicking on Next", "uploadFile": "Upload File", "uploadStatus": "Upload status", "uploadSuccessful": "Upload successful", "whatLikeDo": "What would you like to do?"}, "importJson": "Import JSON", "missingTranslations": "Missing translations", "noQuestionsYet": "No questions have been added yet.", "question": "Question", "redirectsAssociated": "There are redirects associated to this questions, you can view them in the edit form", "search": "Search question"}, "status": {"disabled": "Unmounted", "private": "Collaborators Only", "public": "Published"}, "statusBar": {"contentLanguage": "Content Language", "switchLang": "Change the bot content language. Currently editing: {currentLang}", "trainChatbot": "Train Chatbot", "train": "Train", "training": "Training", "ready": "Ready", "cancelTraining": "Cancel Training", "trainingPending": "Training Pending", "canceling": "Canceling", "trainingError": "Cannot train <PERSON><PERSON><PERSON>"}, "studio": {"content": {"cloneElements": "Clone selected elements", "confirmDeleteItem": "Do you really want to delete {count, number} {count, plural, one {item} other {items}}?", "contentType": "Content Type", "createNew": "Create new {title}", "currentlySearching": "Currently Searching in", "deleteElements": "Delete selected elements", "missingClosingCurlyBrace": "Missing closing curly brace", "import": {"analysis": "Analysis", "clearExisting": "Clear all existing elements, then import those from my file", "compareNbElements": "Your file contains {fileCmsCount} content elements, while this bot contains {cmsCount} elements.", "import": "Import JSON", "missingContentTypes": "Your bot is missing these content types: {types}.", "notAbleToExtractData": "We were not able to extract any data from your file. Either the file is empty, or it doesn't match any known format.", "selectFile": "Select your JSON file", "selectFileMore": "Select a JSON file. It must be exported from the Content page. You will see a summary of modifications when clicking on Next", "updateMissing": "Update or create missing elements present in my file", "upload": "Upload File", "uploadStatus": "Upload status", "whatLikeDo": "What would you like to do?"}, "insertVariable": "Insert Variable", "mustBeDefaultLang": "Content element must be created in your default language first.", "noContent": "There's no content here.", "noContentDefined": "We think you don't have any content types defined.", "noContentYet": "There's no content yet. You can create some using the 'Add' button.", "pleaseReadDoc": "Please {readTheDocs} to see how you can make use of this feature", "readTheDocs": "read the docs", "searchContent": "Search content", "searchIn": "Select a category", "selectContent": "Pick Content", "changeCategory": "Change Category", "sideBar": {"all": "All", "createNew": "Create new {name}", "createNewContent": "Create new content", "filterByType": "Filter by Content Type", "unregisteredWarning": "Content-types is unregistered."}, "switchToDefaultLang": "Switch to {defaultLang} and start editing", "usageModal": {"contentUsage": "Content Usage", "node": "Node"}, "contentTypeWarning": "Please note that this content-type is only supported in {channels}", "tripleBracesWarning": "To add unescaped / raw text here, use {{{...}}} instead of {{...}}"}, "flow": {"invalidName": "Invalid name", "invalidCharacters": "The name contains reserved or invalid characters.", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "zoomToFit": "Fit to screen", "flowProperties": "Flow properties", "addNode": "Add Node", "addToLibrary": "Add to library", "cantDeleteFailure": "You can't delete the failure node.", "cantDeleteStart": "You can't delete the start node.", "cantDeleteSuccess": "You can't delete the success node.", "chips": "Chips", "prevWorkflow": "Go to previous workflow", "nextWorkflow": "Go to next workflow", "endOfWorkflow": "End of the workflow", "errorOccurred": "An error occurred here", "condition": {"addCondition": "Add condition", "backToList": "Back to list", "chooseElement": "Choose an element", "confirmDeleteCondition": "Are you sure to delete this condition?", "editCondition": "Edit Condition", "editTriggers": "<PERSON>s", "listenActiveWorkflow": "Listen in active workflow", "listenActiveWorkflowHelp": "When enabled, this trigger will only be active when the user is in the workflow where it is located.", "noResults": "No results.", "savedAutomatically": "Changes will be saved automatically", "selectCondition": "Select a condition"}, "copiedToBuffer": "Copied to buffer", "disconnectNode": "Disconnect Node", "editQna": "Edit Q&A", "errorWhileSaving": "There was an error while saving, deleting or renaming a flow. Last modification might not have been saved on server. Please reload page before continuing flow edition", "flowWideOnReceives": "flow-wide on {count, plural, one {receive} other {receives}}", "flowWideTransitions": "flow-wide {count, plural, one {transition} other {transitions}}", "filterNodes": "<PERSON><PERSON>", "highlightByName": "Highlight nodes by name", "logs": {"autoRefresh": "Auto-refresh", "downloadArchives": "Download log archive", "loadMore": "Load more"}, "module": {"learnMore": "Learn more", "notFound": "<PERSON><PERSON><PERSON> not found", "notProperlyRegistered": "The module is not properly registered", "tryingToLoad": "It seems like you are trying to load a module that has not been registered. Please make sure the module is registered then restart the bot."}, "node": {"nodeProperties": "Node Properties", "actionParameters": "Action Parameters", "actionServer": "Action Server", "actionServerTooltip": "This is the action server on which the action will be executed", "actionToExecute": "Action to execute", "actionToRun": "Action to run", "actionInstructionParsingError": "Error while parsing instructions: {msg}", "actionArguments": "Executed with these arguments:", "actionNoArguments": "Executed with no arguments:", "add": "add", "addAction": "Add new action", "nameAlreadyExists": "Can't rename node (this name already exists)", "emptyName": "Node name cannot be empty", "confirmOverwriteParameters": "Do you want to overwrite existing parameters?", "contentPaste": "content_paste", "couldNotRetrieveActionServer": "Could not retrieve action servers", "editAction": "Edit Action", "editSkill": "Edit skill", "end": "End", "chatbotExecutes": "Chatbot Executes", "chatbotSays": "<PERSON><PERSON><PERSON>", "triggeredBy": "Workflow is Triggered by", "workflowFails": "Workflow Fails", "workflowSucceeds": "Workflow Succeeds", "errorInServer": "There seems to be an error in your Botpress server. Please contact your administrator.", "errorListingActions": "Error listing actions from the action server", "errorListingActionsMore": "There was an error while trying to get the list of actions on the selected server", "executeCode": "⚡ Execute code", "finishAddAction": "Add Action", "finishUpdateAction": "Update Action", "hasNoParameters": "This action has no parameters", "loadingActionServer": "Please wait, loading action servers...", "message": "Message", "messageToSend": "Message to send", "missingLink": "Missing Link", "noActionsFound": "No actions found on this action server", "noDescription": "No description", "nodeName": "Node Name", "onEnter": "On Enter", "onReceive": "On Receive", "return": "Return", "saySomething": "Say Something", "theBotWill": "The bot will", "transition": {"action": {"endFlow": "End flow", "returnToPreviousFlow": "Return to previous flow", "transitionToNode": "Transition to node", "transitionToSubflow": "Transition to subflow"}, "condition": {"conditionalTransition": "Conditional transition", "always": "Always", "intentIs": "Intent Is {intentName}", "matchesProperty": "Matches Property", "rawExpression": "Raw Expression (advanced)"}, "edit": "Edit condition to transition", "expression": "Expression (ex: !== undefined)", "fieldName": "Field Name (ex: nickname, age)", "javascriptExpression": "Javascript expression", "mustSelectSubflow": "You must select a subflow to transition to", "new": "New condition to transition", "noSpecific": "No specific node", "returnToCallingNode": "Continue calling node execution", "returnToCallingNodeExecute": "Execute calling node again", "returnToNodeCalled": "Return to node called", "specificNodeCalled": "Specific node", "showCondition": "Condition", "specifyCondition": "Specify a condition", "whenMetDo": "When condition is met, do"}, "transitions": "Transitions", "unknownParameterType": "⚠️ Unknown parameter type ({type}). This parameter will be ignored.", "valuePlaceholder": "Value", "waitForUserMessage": "Wait for user message", "youCanChangeActions": "You can change how the Action is executed by providing it parameters. Some parameters are required, some are optional."}, "nodeType": {"action": "Action", "execute": "Execute", "executeAction": "Execute Action", "listen": "Listen", "router": "Router", "say": "Say", "sendMessage": "Send Message", "split": "Split", "standard": "Standard Node", "trigger": "<PERSON><PERSON>"}, "nowSaveAuto": "Pssst! Flows now save automatically, no need to save anymore.", "removeFromLibrary": "Remove from library", "sessionStartsHere": "Every user session starts here", "setAsStart": "Set as Start Node", "sidePanel": {"confirmDeleteFlow": "Are you sure you want to delete the flow {name}", "createFlow": "Create Flow", "createNewFlow": "Create new flow", "createNewTopic": "Create new topic", "duplicateFlow": "Duplicate Flow", "filterFlows": "Filter flows", "filterLibrary": "Filter library", "filterTopicsAndWorkflows": "Filter topics and workflows", "flowName": "Flow Name", "flowNameHelp": "It can only contain letters, numbers, underscores and hyphens. You can also use slashes to create folders (ex: myfolder/mynewflow)", "flowNamePlaceholder": "Choose a name for your flow", "importContent": "Import content", "nameInUse": "Name already in use", "nameInUseMessage": "A flow with that name already exists. Please choose another one.", "node": "Node", "renameFlow": "Rename Flow", "importTopic": "Import Topic", "addTopic": "Add Topic", "addWorkflow": "Add Workflow", "tapIconsToAdd": "Tap icons in the toolbar to import or add your first topic.", "renameWorkflow": "Rename Workflow", "renameTopic": "Rename <PERSON>", "nameWorkflow": "Name Workflow", "nameTopic": "Name Topic"}, "skills": {"couldNotLoad": "Could not load skills view", "edit": "Edit a skill", "error": "ERROR – Skill \"{size}\" is an invalid size for Skill window. Valid sizes are {sizes}.", "generatingSkillFlow": "Generating your skill flow...", "insert": "Insert a new skill"}, "toolbar": {"clickDetails": "Click for more details", "currentlyEditing": "{name} is currently editing this flow", "missingDetails": "Missing {nb} links", "problemsWithFlow": "There are some problems with your flow.", "renamingAndDeletingDisabled": "Renaming and Deleting flows is disabled", "somebodyIsEditing": "Somebody is editing another flow", "whenDiscussionTimeouts": "When a discussion timeouts (user doesn't answer in the configured timeframe) he will be redirected here.", "whenConversationEnds": "When a conversation ends (no transition) he will be redirected here.", "whenErrorEncountered": "When an error is encountered in the flow, the user is redirected here", "salesCallToAction": "Start a free Enterprise trial", "salesCallToActionDescription": "Secure, scalable, entreprise-grade chatbots"}, "topic": "Topic", "topicEditor": {"alreadyExistButDifferent": "These elements already exist but are different", "contentOverview": "Content Overview", "couldNotParseFile": "Could not parse JSON file: {msg}", "createNewTopic": "Create a new topic", "createTopic": "Create topic", "dontExisteWillCreate": "These elements don't exist and will be created", "editTopic": "Edit topic", "importContent": "Import content", "importedSuccessfully": "{detected} imported successfully!", "noChangesToApply": "There are no changes to apply, or everything in the file is identical to the existing content", "overwrite": "Overwrite existing content", "selectJson": "Select your JSON file", "topicName": "Topic Name", "topicNameHelp": "Choose a broad name to represent your topic, for example HR or IT. You can rename it later", "unknownFileType": "Unknown file type or invalid format", "willBeCreated": "This element doesn't exist and will be created", "willBeOverwritten": "An element with that name already exist. Content will be overwritten", "workflowName": "Workflow Name"}, "topicList": {"bigWarning": "WARNING:", "confirmDeleteFlow": "Are you sure you want to delete this workflow?", "confirmDeleteTopic": "Are you sure you want to delete this topic? Its Q&A and workflows will be deleted as well.", "createNewWorkflow": "Create new workflow", "defaultWorkflows": "Default Workflows", "deleteTopic": "Delete Topic", "deleteWorkflow": "Delete Workflow", "editQna": "Edit Q&A", "editTopic": "Edit topic", "editWorkflow": "Edit Workflow", "exportTopic": "Export Topic", "flowsAssociatedDelete": "{warning} {count} flows associated with the topic will be deleted", "importExisting": "Import existing workflow", "nbQuestionsInTopic": "Number of questions in that topic", "nbTriggersInWorkflow": "Number of NLU triggers on that workflow", "workflowReceiving": "Workflows referencing this workflow:"}, "transition": "Transition", "unauthUpdate": "Unauthorized flow update. You have insufficient role privileges to modify flows.", "workflow": {"create": "Create Workflow", "edit": "Edit Workflow - {name}", "labelHelp": "The label is a friendly name that can replace the name in the topic list", "name": "Workflow name"}}, "sideBar": {"config": "Config", "content": "Content", "flows": "Flows", "nlu": "Natural language understanding"}}, "toolbar": {"emulator": "Emulator", "help": "Help", "bottomPanel": "Bottom Panel", "readDoc": "Read Documentation", "showEmulator": "Show Emulator", "toggleBottomPanel": "Toggle Bottom Panel", "toggleEmulator": "Toggle Emulator", "toggleSidePanel": "Toggle Side Panel", "tutorial": "Tutorial", "docs": "Documentation", "forum": "Forum"}, "bottomPanel": {"inspector": {"autoExpand": "Auto-expand all nodes"}, "closePanel": "Close Panel", "logs": {"scrollToFollow": "Scroll to follow logs", "downloadLogs": "Download Logs", "clearHistory": "Clear log history", "endOfLogs": "End of logs", "debug": {"confUpdated": "Debug configuration updated successfully!"}, "filter": "filter", "botLevelOBS": "OBS: A log is considered to be from bot if bp.logger.forBot(...) is used"}, "debugger": {"fetchingEvent": "Fetching event...", "topIntents": "Top Intents", "topPredictions": "Top Predictions", "displayDebugging": "Display debugging in workflows", "autoFocus": "Auto-focus on new messages", "newSession": "Create a new session", "splashMessage": "Engage conversation with your chatbot and click on any message to inspect its behaviors.", "unauthorized": "Unauthorized", "unauthorizedMessage": "You lack sufficient permissions to inspect events.", "unauthorizedMessage2": "Permission required: write access on \"module.extensions\"", "settings": {"confUpdated": "Configuration updated successfully!", "confUpdatedError": "There was an error parsing your configuration. Please validate the syntax", "payloadSent": "Payload sent successfully!", "payloadSentError": "There was an error parsing your payload. Please validate the syntax", "editConf": "Edit Configuration", "editConfHelper": "Test temporary configuration changes. Refresh the page to reset.", "editConfPlaceholder": "Change Webchat Settings (must be valid json)", "saveConf": "Save Configuration", "sendRawPayloads": "Send Raw Payloads", "sendRawPayloadsHelper": "Send any valid JSON message, to test custom events, for example", "sendRawPayloadsPlaceholder": "Valid JSON Payload", "saveRawPayloads": "Send Payload", "alwaysShowDebugger": "Always show Debugger", "updateDebuggerOnNew": "Update debugger on new message", "userId": "User ID", "userIdHelper": "Changes the User ID stored on your browser", "userIdPlaceholder": "Your User ID", "authToken": "External Auth Token", "authTokenHelper": "It must be a valid JWT Token", "authTokenPlaceholder": "Token generated from your system", "save": "Save", "settings": "Settings", "basic": "Basic", "advanced": "Advanced"}, "processing": {"afterMW": "After Middleware", "beforeMW": "Before Middleware", "received": "Event Received", "stateLoaded": "Loaded User State", "hook": "Hook", "mw": "Middleware", "dialog": "Processing Dialog", "action": "Action", "completed": "Event Processing Completed", "executedIn": "Executed in {n} ms", "type": "Type", "stacktrace": "Stacktrace"}, "actions": {"say": "Say {x}", "startWorkflow": "Start workflow {x}", "goToNode": "Go to node {x}", "redirectTo": "Redirect to {x}", "continue": "Continue", "cancelPrompt": "Cancel Prompt", "informPrompt": "Inform Prompt"}, "dialog": {"decision": "Decision", "suggestions": "Suggestions", "flowNodes": "Flow Nodes", "dialogManager": "Dialog Manager"}, "entities": {"type": "Type", "source": "Source", "normalizedValue": "Normalized Value"}, "inspector": {"copyEventPath": "Copy Event Path", "expandAll": "Expand All"}, "notAvailable": "Not available", "ndu": {"topTriggers": "Top Triggers", "decisionsTaken": "Decisions Taken", "sendKnowledge": "Send knowledge {x}", "startWorkflow": "Start workflow {x}", "goToNode": "Go to node {x}", "redirectTo": "Redirect to {x}", "continueFlowExecution": "Continue flow execution", "informCurrentPrompt": "Inform current prompt", "cancelCurrentPrompt": "Cancel current prompt", "noResults": "No results", "dialogUnderstanding": "Dialog Understanding"}, "nlu": {"intentsVeryClose": "Predicted intents are very close.", "youCanAccountForIt": "You can account for it checking the variable : ", "ambiguous": "Ambiguous", "noModel": "Failed at running NLU as no model was trained", "languageUnderstanding": "Language Understanding"}, "slots": {"slot": "Slot", "source": "Source", "extracted": "Extracted", "value": "Value: {x}", "turnsAgo": "{x} turns ago", "thisTurn": "This turn"}, "summary": {"cannotDisplay": "Cannot display event summary", "state": "State", "errors": "Errors"}, "triggers": {"insideTopic": "Inside Topic", "outsideTopic": "Outside Topic", "insideWorkflow": "Inside Workflow", "qna": "QnA", "wf": "WF", "node": "Node"}, "eventNotFound": {"title": "Event not found", "message": "The requested event was not found. Possible reasons:", "message2": "The Event Collector is not enabled in Botpress Config", "message3": "The event was pruned from the database"}}}}