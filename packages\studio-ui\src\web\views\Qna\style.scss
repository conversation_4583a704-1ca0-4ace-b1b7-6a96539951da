.content {
  flex-grow: 1;
  margin-top: var(--spacing-large);
  overflow-y: auto;

  &.empty {
    display: flex;
  }
}

:global(.qna-dialog) {
  display: flex;
  padding: 0 !important;

  & > div {
    flex-grow: 1;
    height: auto;
  }
}

.highlightedQna {
  margin-bottom: var(--spacing-large);

  .questionWrapper {
    border: 1px solid var(--ocean);
  }
}

.header :global(.more-options-selected-option) {
  background: var(--hover-ocean) !important;

  &:hover {
    background: var(--hover-ocean) !important;
  }

  svg {
    fill: var(--shark) !important;
  }
}

.contextSelector {
  grid-column: 1/3;
  margin-bottom: var(--spacing-x-large);

  label {
    font-size: 12px;
    font-weight: 400;
    line-height: 1.25;
    color: var(--shark);
    margin: 0 0 var(--spacing-medium);
  }

  :global(.bp3-input) {
    background: #fff;
    border-radius: 3px;
    border: 1px solid var(--gray);
    box-shadow: none;
    color: var(--shark);
    font-size: 14px;
    font-weight: 300;
    line-height: 22px;
    margin: 0;
    padding: var(--spacing-x-small) var(--spacing-small);
    transition: border 0.3s;

    &:hover {
      border: 1px solid var(--reef);
    }

    &:focus,
    &:focus-within {
      outline-color: transparent;
      outline-style: none;
      font-weight: 300;
      font-style: normal;
      border: 1px solid var(--ocean);
    }
  }
}

.errorsList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.redirectTitle {
  margin-top: var(--spacing-x-large);
  grid-column: 1 / 3;
  font-size: 14px;
  font-weight: 500;
  flex-basis: 100%;
}

.initialLoading {
  height: 100%;
  width: 100%;
}

.loading {
  margin-bottom: var(--spacing-large);
}

.initialLoading,
.loading {
  :global {
    .bp3-spinner-track {
      stroke: var(--gray) !important;
    }

    .bp3-spinner-head {
      stroke: var(--reef) !important;
    }
  }
}

:global {
  .header-content-tooltip {
    .bp3-popover-arrow svg path {
      fill: var(--seashell) !important;
    }

    .bp3-popover-content {
      background: var(--seashell) !important;
    }
  }
}

.searchWrapper {
  align-items: flex-start;
  display: flex;
  justify-content: flex-end;
  margin-right: var(--spacing-large);

  .input,
  .contextInput {
    background: #fff;
    border-radius: 3px;
    border: 1px solid var(--gray);
    color: var(--shark);
    font-size: 14px;
    font-weight: 300;
    line-height: 22px;
    margin: 0;
    padding: var(--spacing-x-small) var(--spacing-small);
    transition: border 0.3s;
    width: 200px;

    &:hover {
      border: 1px solid var(--reef);
    }

    &:focus,
    &:focus-within {
      outline-color: transparent;
      outline-style: none;
      font-weight: 300;
      font-style: normal;
      border: 1px solid var(--ocean);
    }
  }

  ::-webkit-input-placeholder {
    color: var(--gray);
  }
  ::-moz-placeholder {
    color: var(--gray);
  }
  :-ms-input-placeholder {
    color: var(--gray);
  }
  :-moz-placeholder {
    color: var(--gray);
  }

  .contextInput {
    margin-left: var(--spacing-large);
    position: relative;

    :global {
      .bp3-tag-input,
      .bp3-tag-input-values,
      input {
        display: block;
        font-size: 14px;
        font-weight: 300;
        line-height: 22px;
        background: none;
        padding: 0 !important;
        margin: 0;
        min-height: 0;
        border: 0;
        box-shadow: none;
        height: auto;
        width: 100%;
      }

      .bp3-transition-container {
        top: 100% !important;
        right: 0 !important;
        transform: none !important;
        left: auto !important;
        margin-top: 1px;
      }

      .bp3-menu {
        max-height: 200px;
        overflow: auto;
      }
    }
  }
}

.questionWrapper {
  background: #fff;
  border-radius: 5px;
  margin-bottom: var(--spacing-large);
  width: 100%;

  .collapsibleWrapper {
    display: grid;
    grid-column-gap: var(--spacing-xx-large);
    grid-template-columns: 1fr 1fr;
    margin-top: var(--spacing-large);
    padding: 0 var(--spacing-x-large) var(--spacing-x-large);
  }

  .items {
    line-height: 0;
  }

  .addBtn {
    color: var(--ocean);
    display: block;
    font-size: 12px;
    font-style: normal;
    font-weight: normal;
    line-height: 1.25;
    margin-top: var(--spacing-medium);
    transition: background 0.3s;

    svg {
      fill: var(--ocean);
    }

    &:hover,
    &:focus {
      color: var(--ocean);
      background: var(--hover-ocean);
    }
  }

  .headerWrapper {
    display: flex;
    align-items: center;

    :global(.more-options-btn) {
      right: 0;
      margin-right: var(--spacing-x-large);
      position: relative;
    }

    :global(.more-options-more-menu) {
      right: var(--spacing-small);
      top: 50%;
      margin-top: 20px;
    }
  }

  .questionHeader {
    flex-grow: 1;
    justify-content: flex-start;
    padding: var(--spacing-x-large);
    padding-right: var(--spacing-xxx-large);

    &:hover {
      background: #fff;
    }

    :global(.bp3-button-text) {
      display: flex;
      min-height: 20px;
      width: 100%;
    }

    .left {
      align-items: center;
      display: flex;
      flex-grow: 1;
      overflow: hidden;
      padding-right: var(--spacing-large);

      h1 {
        color: var(--shark);
        display: block;
        flex-grow: 0;
        flex-shrink: 1;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.29;
        margin: 0 0 0 var(--spacing-medium);
        overflow: hidden;
        padding: 0;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;

        .refTitle {
          color: var(--reef);
        }
      }
    }

    .right :global(.bp3-popover-wrapper) + :global(.bp3-popover-wrapper) .tag,
    .right :global(.bp3-popover-wrapper) + .tag,
    .right .tag + :global(.bp3-popover-wrapper) .tag {
      margin-left: var(--spacing-medium);
    }

    .tag {
      background: var(--seashell);
      border-radius: 3px;
      color: var(--shark);
      display: inline-block;
      font-size: 8px;
      height: 20px;
      line-height: 20px;
      padding: 1px 6px 0;
      text-transform: uppercase;
      white-space: nowrap;

      & + .tag {
        margin-left: var(--spacing-medium);
      }

      &.warning {
        color: var(--lighthouse);
        background: var(--hover-lighthouse-30);
      }
      &.qnaId {
        margin-left: var(--spacing-medium);
      }
    }

    .right {
      align-items: center;
      display: flex;
      flex-grow: 0;
      flex-shrink: 1;
    }
  }

  h2 {
    font-size: 12px;
    line-height: 1.25;
    color: var(--shark);
    margin: 0 0 var(--spacing-medium);
  }

  .errorIcon {
    position: absolute;
    right: var(--spacing-large);
    top: 50%;
    transform: translate(0, -50%);

    svg {
      fill: var(--lighthouse) !important;
    }
  }

  .textareaWrapper {
    position: relative;

    &:focus-within {
      & + .textareaWrapper {
        .textarea,
        .contentAnswer {
          border-top-color: transparent;
        }
      }

      .textarea,
      .contentAnswer {
        border-top-color: var(--ocean);
        border-bottom-color: var(--ocean);

        &.hasError {
          border-bottom-color: var(--lighthouse);

          &:focus {
            border-top-color: var(--lighthouse);

            & + .textarea {
              border-top-color: transparent;
            }
          }
        }
      }
    }
  }

  .textarea,
  .contentAnswer {
    background: transparent;
    border: none;
    border-bottom: 1px solid var(--gray);
    border-top: 1px solid var(--gray);
    color: var(--shark);
    font-size: 12px;
    height: 39px;
    line-height: 1.25;
    margin: -1px 0 0;
    padding: var(--spacing-large) 2%;
    padding-left: 0;
    resize: none;
    transition: border 0.3s;
    width: 100%;
  }

  .contentAnswer {
    display: flex;
    padding: 0;

    :global {
      .bp3-input {
        background: none;
        border: none;
        color: var(--shark) !important;
        font-size: 12px;
        height: 100%;
        padding: var(--spacing-large) 2%;
        padding-left: 0;
      }

      .bp3-button {
        background: none;
        border: 0;
        box-shadow: none;
      }

      .bp3-control-group {
        height: 100%;
        flex-grow: 1;
      }
    }
  }
}
