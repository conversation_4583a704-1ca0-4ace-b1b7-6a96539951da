name: Run Client Tests

on: pull_request

permissions:
  id-token: write
  contents: read

jobs:
  run-client-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    env:
      NODE_OPTIONS: '--max_old_space_size=8192'
    steps:
      - uses: actions/checkout@v2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          extra_filters: '-F @botpress/client'
      - run: |
          # pnpm -F client exec puppeteer browsers install chrome
          pnpm -F client run test:e2e
