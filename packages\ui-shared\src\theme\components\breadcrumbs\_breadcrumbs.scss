// Copyright 2016 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "~@blueprintjs/icons/src/icons";
@import "../../common/variables";

/*
Breadcrumbs

Markup:
<ul class="#{$ns}-breadcrumbs">
  <li><a class="#{$ns}-breadcrumbs-collapsed" href="#"></a></li>
  <li><a class="#{$ns}-breadcrumb #{$ns}-disabled">Folder one</a></li>
  <li><a class="#{$ns}-breadcrumb" href="#">Folder two</a></li>
  <li><a class="#{$ns}-breadcrumb" href="#">Folder three</a></li>
  <li><span class="#{$ns}-breadcrumb #{$ns}-breadcrumb-current">File</span></li>
</ul>

Styleguide breadcrumbs
*/

.#{$ns}-breadcrumbs {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  // unstyled inline list reset
  margin: 0;
  cursor: default;
  height: $pt-input-height;
  padding: 0;
  list-style: none;

  // descendant selector because nothing should come between ul and li
  > li {
    display: flex;
    align-items: center;

    &::after {
      display: block;
      margin: 0 ($pt-grid-size / 2);
      background: svg-icon("16px/chevron-right.svg", (path: (fill: $pt-icon-color)));
      width: $pt-icon-size-standard;
      height: $pt-icon-size-standard;
      content: "";
    }

    &:last-of-type::after {
      display: none;
    }
  }
}

.#{$ns}-breadcrumb,
.#{$ns}-breadcrumb-current,
.#{$ns}-breadcrumbs-collapsed {
  display: inline-flex;
  align-items: center;
  font-size: $pt-font-size-large;
}

.#{$ns}-breadcrumb,
.#{$ns}-breadcrumbs-collapsed {
  color: $pt-text-color-muted;
}

.#{$ns}-breadcrumb {
  &:hover {
    text-decoration: none;
  }

  &.#{$ns}-disabled {
    cursor: not-allowed;
    color: $pt-text-color-disabled;
  }

  .#{$ns}-icon {
    margin-right: $pt-grid-size / 2;
  }
}

.#{$ns}-breadcrumb-current {
  color: inherit;
  font-weight: 600;

  .#{$ns}-input {
    vertical-align: baseline;
    font-size: inherit;
    font-weight: inherit;
  }
}

.#{$ns}-breadcrumbs-collapsed {
  margin-right: 2px;
  border: none;
  border-radius: $pt-border-radius;
  background: $light-gray1;
  cursor: pointer;
  padding: 1px ($pt-grid-size / 2);
  vertical-align: text-bottom;

  &::before {
    display: block;
    background: svg-icon("16px/more.svg", (circle: (fill: $pt-icon-color))) center no-repeat;
    width: $pt-icon-size-standard;
    height: $pt-icon-size-standard;
    content: "";
  }

  &:hover {
    background: $gray5;
    text-decoration: none;
    color: $pt-text-color;
  }
}

.#{$ns}-dark {
  .#{$ns}-breadcrumb,
  .#{$ns}-breadcrumbs-collapsed {
    color: $pt-dark-text-color-muted;
  }

  .#{$ns}-breadcrumbs > li::after {
    color: $pt-dark-icon-color;
  }

  .#{$ns}-breadcrumb.#{$ns}-disabled {
    color: $pt-dark-text-color-disabled;
  }

  .#{$ns}-breadcrumb-current {
    color: $pt-dark-text-color;
  }

  .#{$ns}-breadcrumbs-collapsed {
    background: rgba($black, 0.4);

    &:hover {
      background: rgba($black, 0.6);
      color: $pt-dark-text-color;
    }
  }
}
