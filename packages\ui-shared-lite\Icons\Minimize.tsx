// @ts-nocheck
import React from 'react'

export default () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
    <path
      fillRule="evenodd"
      d="M3.59,5 L2,5 C1.45,5 1,5.45 1,6 C1,6.55 1.45,7 2,7 L6,7 C6.55,7 7,6.55 7,6 L7,2 C7,1.45 6.55,1 6,1 C5.45,1 5,1.45 5,2 L5,3.59 L1.71,0.3 C1.53,0.11 1.28,-9.30366895e-14 1,-9.30366895e-14 C0.45,-9.30366895e-14 -2.40496512e-12,0.45 -2.40496512e-12,1 C-2.40496512e-12,1.28 0.11,1.53 0.29,1.71 L3.59,5 Z M1,16 C1.28,16 1.53,15.89 1.71,15.71 L5,12.41 L5,14 C5,14.55 5.45,15 6,15 C6.55,15 7,14.55 7,14 L7,10 C7,9.45 6.55,9 6,9 L2,9 C1.45,9 1,9.45 1,10 C1,10.55 1.45,11 2,11 L3.59,11 L0.3,14.29 C0.11,14.47 -2.51954013e-12,14.72 -2.51954013e-12,15 C-2.51954013e-12,15.55 0.45,16 1,16 Z M10,15 C10.55,15 11,14.55 11,14 L11,12.41 L14.29,15.7 C14.47,15.89 14.72,16 15,16 C15.55,16 16,15.55 16,15 C16,14.72 15.89,14.47 15.71,14.29 L12.41,11 L14,11 C14.55,11 15,10.55 15,10 C15,9.45 14.55,9 14,9 L10,9 C9.45,9 9,9.45 9,10 L9,14 C9,14.55 9.45,15 10,15 Z M10,7 L14,7 C14.55,7 15,6.55 15,6 C15,5.45 14.55,5 14,5 L12.41,5 L15.71,1.71 C15.89,1.53 16,1.28 16,1 C16,0.45 15.55,1.06803455e-13 15,1.06803455e-13 C14.72,1.06803455e-13 14.47,0.11 14.29,0.29 L11,3.59 L11,2 C11,1.45 10.55,1 10,1 C9.45,1 9,1.45 9,2 L9,6 C9,6.55 9.45,7 10,7 Z"
    />
  </svg>
)
