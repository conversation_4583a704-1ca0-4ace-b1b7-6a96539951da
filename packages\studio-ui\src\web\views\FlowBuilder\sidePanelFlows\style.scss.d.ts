// This file is automatically generated.
// Please do not change this file!
interface CssExports {
  'addWorkflowNode': string;
  'emptyState': string;
  'grabbable': string;
  'mainoverlay': string;
  'modalHeader': string;
  'overhidden': string;
  'referencedWorkflows': string;
  'rightPanel': string;
  'rightPanelActive': string;
  'section': string;
  'sidePanel': string;
  'tabs': string;
  'tag': string;
  'title': string;
  'toolItem': string;
  'toolPanel': string;
  'topicName': string;
  'tree': string;
  'treeNode': string;
}
declare var cssExports: CssExports;
export = cssExports;
