name: Setup
description: install dependencies and build

inputs:
  extra_filters:
    description: 'Turbo additional filters to select packages to build'
    required: false

runs:
  using: 'composite'
  steps:
    - uses: pnpm/action-setup@v4
      with:
        version: 8.6.2

    - uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        check-latest: true

    - name: Install dependencies
      shell: ${{ runner.os == 'Windows' && 'pwsh' || 'bash' }}
      run: pnpm i --frozen-lockfile

    - name: Build
      shell: ${{ runner.os == 'Windows' && 'pwsh' || 'bash' }}
      run: pnpm build ${{ inputs.extra_filters }}
