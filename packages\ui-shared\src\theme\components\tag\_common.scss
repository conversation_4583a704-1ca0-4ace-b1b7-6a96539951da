// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

$tag-default-color: $gray1 !default;
$dark-tag-default-color: $gray5 !default;

$tag-height: $pt-grid-size * 2 !default;
$tag-line-height: $pt-icon-size-standard !default;
$tag-padding-top: ($tag-height - $tag-line-height) / 2 !default;
$tag-padding: $tag-padding-top * 3 !default;
$tag-margin: ($pt-input-height - $tag-height) / 2 !default;

$tag-height-large: $pt-grid-size * 3 !default;
$tag-line-height-large: $pt-icon-size-large !default;
$tag-padding-large: ($tag-height-large - $tag-line-height-large) !default;

$tag-icon-spacing: ($tag-height - 12px) / 2 !default;
$tag-icon-spacing-large: ($tag-height-large - $pt-icon-size-standard) / 2 !default;

$tag-round-adjustment: 2px !default;

@mixin pt-tag() {
  @include pt-tag-interactive($tag-default-color, 1);
  @include pt-flex-container(row, $tag-icon-spacing, inline);
  align-items: center;

  position: relative;
  border: none;
  border-radius: $pt-border-radius;
  box-shadow: none;
  background-color: $tag-default-color;
  min-width: $tag-height;
  max-width: 100%;
  min-height: $tag-height;
  padding: $tag-padding-top $tag-padding;
  line-height: $tag-line-height;
  color: $pt-dark-text-color;
  font-size: $pt-font-size-small;

  &:focus {
    @include focus-outline(0);
  }

  &.#{$ns}-round {
    border-radius: $tag-height-large;
    // optical adjustment for rounded tags
    padding-right: $tag-padding + $tag-round-adjustment;
    padding-left: $tag-padding + $tag-round-adjustment;
  }

  .#{$ns}-dark & {
    @include pt-tag-interactive($dark-tag-default-color, 1);

    background-color: $dark-tag-default-color;
    color: $pt-text-color;

    > #{$icon-classes} {
      fill: currentColor;
    }
  }

  > #{$icon-classes} {
    fill: $white;
  }
}

@mixin pt-tag-large() {
  @include pt-flex-margin(row, $tag-icon-spacing-large);
  min-width: $tag-height-large;
  min-height: $tag-height-large;
  padding: 0 $tag-padding-large;
  line-height: $tag-line-height-large;
  font-size: $pt-font-size;

  &.#{$ns}-round {
    // optical adjustment for rounded tags
    padding-right: $tag-padding-large + $tag-round-adjustment;
    padding-left: $tag-padding-large + $tag-round-adjustment;
  }
}

@mixin pt-tag-intent($background-color, $text-color: $white) {
  @include pt-tag-interactive($background-color, 1);

  background: $background-color;
  color: $text-color;
}

@mixin pt-tag-minimal() {
  > #{$icon-classes} {
    fill: $pt-icon-color;
  }

  &:not([class*="#{$ns}-intent-"]) {
    @include pt-tag-minimal-interactive($tag-default-color, 0.2);

    background-color: rgba($gray3, 0.2);
    color: $pt-text-color;

    .#{$ns}-dark & {
      @include pt-tag-minimal-interactive($dark-tag-default-color, 0.2);

      color: $pt-dark-text-color;

      > #{$icon-classes} {
        fill: $pt-dark-icon-color;
      }
    }
  }
}

@mixin pt-tag-minimal-intent($background-color, $text-color, $dark-text-color) {
  @include pt-tag-minimal-interactive($background-color, 0.15);

  background-color: rgba($background-color, 0.15);
  color: $text-color;

  > #{$icon-classes} {
    fill: $background-color;
  }

  .#{$ns}-dark & {
    @include pt-tag-minimal-interactive($background-color, 0.25);

    background-color: rgba($background-color, 0.25);
    color: $dark-text-color;
  }
}

@mixin pt-tag-interactive($background-color, $opacity) {
  &.#{$ns}-interactive {
    cursor: pointer;

    &:hover {
      background-color: rgba($background-color, $opacity - 0.15);
    }

    &.#{$ns}-active,
    &:active {
      background-color: rgba($background-color, $opacity - 0.3);
    }
  }
}

@mixin pt-tag-minimal-interactive($background-color, $opacity) {
  &.#{$ns}-interactive {
    cursor: pointer;

    &:hover {
      background-color: rgba($background-color, $opacity + 0.1);
    }

    &.#{$ns}-active,
    &:active {
      background-color: rgba($background-color, $opacity + 0.2);
    }
  }
}

@mixin pt-tag-remove() {
  display: flex;
  opacity: 0.5;
  // top/bottom to allow for padding to enlarge click area,
  // right to tuck remove button into padding space.
  margin-top: -$tag-padding-top;
  // stylelint-disable-next-line declaration-no-important
  margin-right: -$tag-padding !important;
  margin-bottom: -$tag-padding-top;
  border: none;
  background: none;
  cursor: pointer;
  padding: $tag-padding-top;
  padding-left: 0;
  color: inherit;

  &:hover {
    opacity: 0.8;
    background: none;
    text-decoration: none;
  }

  &:active {
    opacity: 1;
  }

  // CSS API support
  &:empty::before {
    @include pt-icon();
    content: $pt-icon-small-cross;
  }

  .#{$ns}-large & {
    // stylelint-disable-next-line declaration-no-important
    margin-right: -$tag-padding-large !important;
    padding: ($tag-height-large - $pt-icon-size-large) / 2;
    padding-left: 0;

    &:empty::before {
      @include pt-icon-sized($pt-icon-size-large);
    }
  }
}
