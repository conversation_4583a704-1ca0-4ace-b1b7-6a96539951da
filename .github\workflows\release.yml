name: Release
on:
  push:
    branches:
      - master
jobs:
  release_bin:
    name: Release Binaries
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@master

      - uses: actions/setup-node@v2
        with:
          node-version: '12.18.1'
          cache: 'yarn'

      - name: Git Unshallow
        run: git fetch --prune --unshallow

      - name: Fetch Node Packages
        run: |
          yarn --immutable

      - name: Get Release Details
        id: release_details
        uses: botpress/gh-actions/get_release_details@v1

      - name: Display Release Details
        id: changelog
        run: |
          echo "Changelog: ${{ steps.release_details.outputs.changelog }}"
          echo "Is new release?: ${{ steps.release_details.outputs.is_new_release }}"
          echo "Version: ${{ steps.release_details.outputs.version }}"
          echo "Latest Tag: ${{ steps.release_details.outputs.latest_tag }}"

      - name: 'Build and Package'
        if: ${{ steps.release_details.outputs.is_new_release == 'true' }}
        run: |
          yarn build
          yarn package
        env:
          NODE_ENV: production

      - name: 'Release'
        if: ${{ steps.release_details.outputs.is_new_release == 'true' }}
        uses: softprops/action-gh-release@v1
        with:
          files: ./bin/studio-v*
          prerelease: false
          draft: false
          body: ${{ steps.release_details.outputs.changelog }}
          name: v${{ steps.release_details.outputs.version }}
          tag_name: v${{ steps.release_details.outputs.version }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
