.tooltip {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 1.25;

  .shortcutLabel {
    margin-left: 4px;
  }
}

.toolbar {
  background: #fff;
  border-bottom: 1px solid var(--c-neutral--light-2);
  color: var(--c-text--light);
  font-family: Roboto;
  height: 50px;
  width: 100%;
  z-index: 1000;

  .list {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: 10px 15px;
    width: 100%;
    z-index: 15;

    .divider {
      background: var(--c-neutral--light-2);
      margin: 0 10px;
      height: 20px;
      width: 1px;
    }

    .item {
      background: none;
      border: none;
      border-radius: 5px;
      color: var(--c-content-color-darker);
      display: flex;
      align-items: center;
      height: 100%;
      line-height: 0;
      padding: 7px 8px;
      transition: background 0.3s;
      vertical-align: middle;
      white-space: pre;

      &Spacing {
        margin-left: 10px;
      }

      &:hover {
        background: var(--c-neutral--light-2);
      }

      .label {
        margin-left: 5px;
      }

      &.clickable {
        user-select: none;
        cursor: pointer;

        &:hover,
        &.active {
          cursor: pointer;
        }
      }
    }
  }
}

.shortcut {
  background-color: #111;
  color: white;
  padding: 3px 5px;
  font-size: 14px;
  margin: 5px auto;
}

.cta {
  &_btn {
    background-color: rgb(50, 118, 234);
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    padding: 8px;
    margin: 0 8px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    &:hover,
    &:active,
    &:focus {
      text-decoration: none;
      background-color: rgb(202, 215, 245);
      color: rgb(50, 118, 234);
    }
  }
  &_tooltip {
    background: #fff;
    color: #111;
  }
}
