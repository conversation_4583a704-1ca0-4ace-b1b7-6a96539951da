.entityEditorBody {
  display: flex;
  & > div {
    padding: 10px;
  }
  .dataPane {
    flex-grow: 1;
  }
  .regexInputDash {
    height: 30px;
    display: flex;
    align-items: center;
    color: #e1e8ed !important; //to make sure intent doesn't override this
    margin: 0;
    padding: 0 10px;
  }
  .configPane {
    width: 40%;
    background-color: #fbfbfb;
  }
}

.validationTag {
  float: right;
}

.configPopover {
  max-width: 250px;
}

.occurrence {
  margin-bottom: 10px;
  .occurrenceName {
    display: flex;
    align-items: center;
    align-self: stretch;
    margin-right: 5px;
  }
}

.occurrencesList {
  max-height: 70vh;
  overflow-y: auto;
  padding: 2px;
}
