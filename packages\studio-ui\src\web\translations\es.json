{"config": {"additionalDetails": "Detalles de Adicionales", "avatarAndCover": "Avatar y portada", "avatarUploadSuccess": "El avatar del Bot se ha cargado correctamente. Debe guardar el formulario para que los cambios surtan efecto.", "botAvatar": "<PERSON><PERSON>", "botConfiguration": "Configuración del bot", "chooseFile": "Elegir archivo", "configUpdated": "La configuración del bot se actualizó correctamente", "confirmChangeLanguage": "¿Está seguro de que desea cambiar el idioma de su bot de {currentName} a {newName}? Todos los elementos de contenido se copiarán, asegúrese de traducirlos.", "confirmUnmount": "¿Está seguro de que desea desmontar este bot? Todas las funcionalidades de este bot dejarán de estar disponibles.", "contactEmail": "Correo electrónico de contacto", "coverPicture": "Imagen de portada", "coverUploadSuccess": "La imagen de portada se ha subido correctamente. Debe guardar el formulario para que los cambios surtan efecto.", "defaultLanguage": "Idioma predeterminado", "description": "Descripción", "formContainsErrors": "El formulario contiene errores", "language": "Idioma", "linkToPolicy": "Enlace a la política de privacidad", "linkToTerms": "Enlace a los Términos y Condiciones", "phoneNumber": "Número de teléfono", "requireImageFile": "{targetProp} requiere un archivo de imagen", "supportedLanguages": "Idiomas admitidos", "website": "Sitio web", "botId": "Bot id", "copyToClipboard": "Copiada al portapapeles"}, "nlu": {"autoTrain": "Auto Entrenamietno", "cancelTraining": "<PERSON><PERSON>ar <PERSON>", "description": "Presione + en el panel izquierdo para crear una intención.", "entities": {"deleteMessage": "¿Está seguro de que desea eliminar el entity \"{entityName}\" ?", "duplicate": "Duplicar Entity", "examplesLabel": "<PERSON><PERSON><PERSON><PERSON> coincidentes", "examplesPlaceholder": "Agregue ejemplos que coincidan con su patrón (uno por línea)", "filterPlaceholder": "Filtrar entities", "fuzzyLabel": "Opciones de emparejamiento difuso", "fuzzyTooltip": "El algoritmo difuso tolerará pequeños errores (por ejemplo: errores tipográficos) en palabras de 4 caracteres o más. Estricto significa que no se permiten errores.", "loose": "<PERSON><PERSON><PERSON>", "matchCaseLabel": "<PERSON><PERSON><PERSON><PERSON>", "matchCaseTooltip": "Si su patrón distingue mayúsculas de minúsculas", "matchingError": "Algunos ejemplos no coinciden", "matchingSuccess": "Todos los ejemplos coinciden", "medium": "Medio", "nameConflictMessage": "Ya existe una entidad con ese nombre. Por favor, elija otro.", "nameConflictTitle": "Nombre ya en uso", "namePlaceholder": "Nombre del Entity", "new": "Nuevo entity", "newOccurrence": "Nueva ocurrencia", "occurrenceLabel": "Occurrencias", "occurrencePlaceholder": "Escriba un valor (por ejemplo, Chicago)", "occurrenceTooltip": "Una aparición es un valor de la entidad. Cada ocurrencia puede tener varios sinónimos.", "patternInvalid": "Patrón no válido", "patternLabel": "Expresión regular", "patternPlaceholder": "Insertar un patrón válido", "rename": "Renombrar Entity", "selectPlaceholder": "Seleccionar entities...", "sensitiveLabel": "Contiene datos confidenciales", "sensitiveTooltip": "La información confidencial se sustituye por * antes de ser guardado en la base de datos", "strict": "Estricto", "synonymPlaceholder": "Escriba un sinónimo (o más, separado por comas) y pulse Intro", "title": "Entities"}, "intents": {"actionErrorMessage": "No pudo {action} intent", "chooseContainerLabel": "Elija un intent diferente para la condición", "contextSelectorCreateMissing": "crear \"{query}\"", "contextSelectorPlaceholder": "Seleccionar contexto...", "contextSelectorTooltip": "Puede escribir en la barra de selección para agregar un nuevo contexto.", "createLabel": "Crear intent", "deleteConfirmMessage": "¿Está seguro de que desea eliminar el intent\"{intentName}\"?", "exactOnly": "coincidencia exacta sólo", "filterPlaceholder": "Filtrar intents", "hintExactMatch": "Este intent will use {exactOnly}. Para habilitar el aprendizaje automático, añadir al menos {nbMás expresiones.", "hintIgnored": "Este intent será ignorado, empezar a añadir expresiones para que sea entrenable.", "hintResilient": "<PERSON><PERSON><PERSON> {nb} more utterances to make NLU more resilient.", "nameDupe": "Un intent con ese nombre ya existe. Por favor, elija otro.", "nameLabel": "Nombre del intent", "namePlaceholder": "Elija un nombre para su intent", "new": "Nuevo intent", "selectIntentLabel": "Seleccione una intent existente", "selectIntentNoResults": "No existe el intent", "summaryPlaceholder": "Resumen de intent", "title": "Intents", "utterancePlaceholder": "Escriba una oración"}, "slots": {"createTitle": "<PERSON>rear un slot para el intent", "deleteMessage": "¿Está seguro de que desea eliminar este slot y todo el etiquetado asociado de todas las expresiones?", "editTitle": "<PERSON><PERSON>", "emptyState": "No hay slots definidos para este intent", "entitiesLabel": "Entities asociadas", "nameLabel": "Nombre del Slot ", "namePlaceholder": "Escriba un nombre aquí", "new": "Crear slot", "noSlotsToTag": "Selection can't be tagged. Define a slot first.", "save": "Guardar slot", "tagSelectionLabel": "Selección de etiquetas", "tagSelectionMessage": "Haga clic en un slot o use números como métodos abreviados de teclado", "names": {"amountOfMoney": "Cantidad de dinero", "distance": "distancia", "duration": "duración", "email": "email", "number": "número", "ordinal": "ordinal", "phoneNumber": "teléfono", "quantity": "cantidad", "temperature": "temperatura", "time": "tiempo", "url": "url", "volume": "volumen", "any": "cualquier"}}, "title": "Reconocimiento del Lenguaje", "trainNow": "<PERSON><PERSON><PERSON> ahora"}, "qna": {"addNew": "<PERSON><PERSON><PERSON>ue<PERSON>", "answer": "Respuesta", "confirmDelete": "¿Quieres eliminar la pregunta?", "context": {"canTypeToCreate": "Puede escribir en la barra de selección para agregar nuevos contextos.", "createQuery": "<PERSON><PERSON><PERSON> contexto", "filterByContexts": "Filtrar por contextos", "selectContext": "Seleccionar contexto...", "title": "Contextos"}, "contexts": "Contextos", "create": "Crear una nueva Q&A", "edit": "Editar Q&A", "editor": {"andOr": "y / o", "answers": "Respuestas", "botWillSay": "<PERSON><PERSON> dirá: ", "checkboxRequired": "Se requiere una casilla de verificación de acción", "duplicatesNotAllowed": "No se permiten preguntas duplicadas.", "incorrectRedirection": "Redirección <PERSON>", "inputsRequred": "Se requieren entradas.", "missingTranslations": "<PERSON><PERSON><PERSON> t<PERSON>", "node": "Nodo", "pasteQuestionHere": "Escriba/Pegue sus preguntas aquí separadas con una nueva línea", "questions": "Preguntas", "redirectToFlow": "Redireccionar al flujo", "typePressAddAnswer": "Escriba y presione enter para agregar una respuesta. Utilice ALT-Enter para una nueva línea"}, "exportToJson": "Exportar en JSON", "form": {"a": "A", "addAnswerAlternative": "Agregar alternativas de respuesta", "addContent": "Agregar contenido", "addMoreQuestionsPlural": "Agregue {count} preguntas más para que sus preguntas y respuestas sean más resistentes", "addMoreQuestionsSingular": "Agregue 1 pregunta más para que sus preguntas y respuestas sean más resistentes", "addOneItemTooltip": "Agrega un item primero", "addQuestion": "Agregar pregunta", "addQuestionAlternative": "Agregar alternativas de preguntas", "cantBeSaved": "No se puede guardar", "chatbotWillRandomlyChoose": "Chatbot responderá aleatoriamente a partir de estas alternativas", "confirmDeleteQuestion": "¿Está seguro de que desea eliminar esta pregunta? También se eliminarán las alternativas de preguntas y respuestas.", "copyIdToClipboard": "Copiar ID al portapapeles", "deleteQuestion": "Eliminar pregunta", "disabledTooltip": "Este Q&A no se mostrará a los usuarios.", "disableQuestion": "Deshabilitar pregunta", "disableRedirection": "Deshabilitar la redirección", "duplicateAnswer": "Ya has escrito esta respuesta", "duplicateQuestion": "Ya has escrito esta pregunta", "emptyState": "Toque + en la barra de herramientas para agregar su primera pregunta.", "enableQuestion": "Habilitar pregunta", "enableRedirection": "Habilitar redirección", "idCopiedToClipboard": "ID copiado al portapapeles", "incomplete": "Incompleto", "incompleteTooltip": "Esta sesión de preguntas y respuestas solo utilizará coincidencias exactas.", "missingAnswer": "Si deja el campo de respuesta vacío, se desactivará esta pregunta.", "missingQuestion": "Si deja el campo de la pregunta vacío, se desactivará esta pregunta.", "node": "Nodo", "noResultsFromFilters": "No se encontraron preguntas", "onlyOneLanguage": "Solo tienes un idioma", "pickNode": "Seleccione un nodo", "pickWorkflow": "Seleccione un flujo de trabajo", "q": "Q", "quickAddAlternative": "Presiona {shortcut} mientras estás en línea para agregar una nueva alternativa rápidamente", "redirectQuestionTo": "Redirigir pregunta a", "redirectToWorkflow": "Redirigir al flujo de trabajo", "translate": "Traducir", "workflow": "Flujo de trabajo", "writeAtLeastTwoMoreQuestions": "Escriba al menos 2 preguntas alternativas para habilitar el aprendizaje automático", "writeFirstQuestion": "Escriba una oración que su usuario pueda escribir para hacer su pregunta", "writeTheAnswer": "Escribe la respuesta a la pregunta", "writingSameQuestion": "Escribir la misma pregunta dos veces deshabilitará esta sesión de preguntas y respuestas."}, "fullName": "Q&A", "hint": {"addMoreQuestions": "Añadir {máspreguntas} para que tu Q&A sean más resistentes.", "exactMatchOnly": "solo coincidencia exacta", "moreQuestions": "Perman<PERSON><PERSON> más 'remanentes, plurales, una 'pregunta' otras 'preguntas'", "willBeExact": "Esta Q&A usará {exactMatchOnly}. Para permitir el aprendizaje automático, añada al menos {remaining} más {remaining, plural, one {pregunta} other {preguntas}}", "willBeIgnored": "Esta Q&A será ignorada, comienza a agregar preguntas para que sea entrenable."}, "import": {"analysis": "<PERSON><PERSON><PERSON><PERSON>", "botContains": "El bot contiene preguntas {qnaCount} y elementos de contenido {cmsCount}.", "clearQuestionsThenInsert": "Borrar preguntas existentes, luego insertar mis nuevas preguntas y crear/actualizar elementos de contenido", "clearQuestionsAnalyticsWarning": "Si se aclaran las preguntas, no estarán disponibles en el informe de análisis", "fileContains": "Su archivo contiene preguntas {fileQnaCount} y elementos de contenido {fileCmsCount}.", "insertNewQuestions": "Insertar las nuevas preguntas de mi archivo y crear/actualizar elementos de contenido asociados", "notAbleToExtract": "No hemos podido extraer ningún dato de su archivo. O el archivo está vacío o no coincide con ningún formato conocido.", "selectJson": "Seleccione su archivo JSON", "selectJsonHelp": "Seleccione un archivo JSON exportado desde el módulo Q&A. Verá un resumen de los cambios al hacer clic en Siguiente", "uploadFile": "Cargar archivo", "uploadStatus": "Estado de carga", "uploadSuccessful": "Subir con éxito", "whatLikeDo": "¿Qué te gustaría hacer?"}, "importJson": "Importar JSON", "missingTranslations": "Missing translations", "noQuestionsYet": "Aún no se han a<PERSON><PERSON><PERSON> pre<PERSON>.", "question": "Pregunta", "redirectsAssociated": "Hay redirecciones asociadas con estas preguntas, se pueden ver en el formulario de edición", "search": "Buscar una pregunta"}, "status": {"disabled": "Desmontado", "private": "Solo para colaboradores", "public": "Publicado"}, "statusBar": {"contentLanguage": "Lenguaje de contenido", "switchLang": "Cambie el idioma del contenido del bot. Actualmente editando: {currentLang}", "trainChatbot": "<PERSON><PERSON><PERSON>", "train": "Entrenar", "training": "Entrenamiento", "ready": "Listo", "cancelTraining": "Cancelar entrenamiento", "trainingPending": "Entrenamiento pendiente", "canceling": "Cancelando", "trainingError": "No se pudo entrenar el Chatbot"}, "studio": {"content": {"cloneElements": "Clonar elementos seleccionados", "confirmDeleteItem": "¿Realmente desea eliminar {count, number} {count, plural, one {item} other {items}}?", "contentType": "Tipo de Contenido", "createNew": "<PERSON><PERSON><PERSON> nuevo {title}", "currentlySearching": "Actualmente buscando en", "deleteElements": "Eliminar elementos seleccionados", "missingClosingCurlyBrace": "Falta la llave de cierre", "import": {"analysis": "Analysis", "clearExisting": "Borrar todos los elementos existentes, luego importarlos de mi archivo", "compareNbElements": "Su archivo contiene elementos de contenido {fileCmsCount}, mientras que este bot contiene elementos {cmsCount}.", "import": "Importar JSON", "missingContentTypes": "A su bot le faltan estos tipos de contenido: {types}.", "notAbleToExtractData": "No pudimos extraer ningún dato de su archivo. El archivo está vacío o no coincide con ningún formato conocido.", "selectFile": "Seleccione su archivo JSON", "selectFileMore": "Seleccione un archivo JSON. Debe exportarse desde la página de contenido. Verá un resumen de las modificaciones al hacer clic en Siguiente", "updateMissing": "Actualizar o crear elementos faltantes presentes en mi archivo", "upload": "Subir archivo", "uploadStatus": "Estado de carga", "whatLikeDo": "¿Que te gustaría hacer?"}, "insertVariable": "Insertar variable", "mustBeDefaultLang": "El elemento de contenido debe crearse primero en su idioma predeterminado.", "noContent": "Aquí no hay contenido.", "noContentDefined": "Creemos que no tiene ningún tipo de contenido definido.", "noContentYet": "Aún no hay contenido. Puede crear algunos usando el botón 'Agregar'.", "pleaseReadDoc": "Por favor {readTheDocs} para ver cómo puede hacer uso de esta función", "readTheDocs": "lea la documentación", "searchContent": "Buscar Contenido", "searchIn": "Seleccionar una categoría", "selectContent": "Elegir contenido", "changeCategory": "Cambiar categoría", "sideBar": {"all": "Todo", "createNew": "<PERSON><PERSON><PERSON> nuevo {name}", "createNewContent": "Crear nuevo contenido", "filterByType": "Filtrar por tipo de contenido", "unregisteredWarning": "El tipo de contenido no está registrado."}, "switchToDefaultLang": "<PERSON>bie a {defaultLang} y comience a editar", "usageModal": {"contentUsage": "Uso de contenido", "node": "Nodo"}, "contentTypeWarning": "Tenga en cuenta que este tipo de contenido solo es compatible con {channels}"}, "flow": {"zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "Encogerse", "zoomToFit": "Ajustar a la pantalla", "flowProperties": "Propiedades de flujo", "addNode": "<PERSON><PERSON><PERSON>", "addToLibrary": "Añadir a la biblioteca", "cantDeleteFailure": "No puede eliminar el nodo de falla.", "cantDeleteStart": "No se puede eliminar el nodo inicial.", "cantDeleteSuccess": "No puede eliminar el nodo de éxito.", "chips": "Chips", "prevWorkflow": "Ir al flujo de trabajo anterior", "nextWorkflow": "Ir al siguiente flujo de trabajo", "endOfWorkflow": "Fin del flujo de trabajo", "errorOccurred": "Ocurrió un error aquí", "condition": {"addCondition": "Agregar condición", "backToList": "Volver a la lista", "chooseElement": "Elige un elemento", "confirmDeleteCondition": "¿Estás seguro de eliminar esta condición?", "editCondition": "Editar condición", "editTriggers": "Editar disparadores", "listenActiveWorkflow": "Escuchar en el flujo de trabajo activo", "listenActiveWorkflowHelp": "Cuando está habilitado, este disparador solo estará activo cuando el usuario esté en el flujo de trabajo donde se encuentra.", "noResults": "No hay resultados.", "savedAutomatically": "Los cambios se guardarán automáticamente", "selectCondition": "Seleccione una condición"}, "copiedToBuffer": "Copiado al buffer", "disconnectNode": "Desconectar nodo", "editQna": "Editar Q&A", "errorWhileSaving": "Se ha producido un error al guardar, eliminar o cambiar el nombre de un flujo. Es posible que la última modificación no se haya guardado en el servidor. Por favor, vuelva a cargar la página antes de continuar la edición de flujo", "flowWideOnReceives": "Recepciones del flujo", "flowWideTransitions": "Transiciones de flujo", "filterNodes": "Filtrar Nodos", "highlightByName": "Resaltar nodos por nombre", "logs": {"autoRefresh": "Auto-refrescar", "downloadArchives": "Descargar archivo de registro", "loadMore": "<PERSON>gar más"}, "module": {"learnMore": "<PERSON>ende más", "notFound": "Módulo no encontrado", "notProperlyRegistered": "El módulo no está registrado correctamente", "tryingToLoad": "Parece que está intentando cargar un módulo que no se ha registrado. Asegúrese de que el módulo está registrado y reinicie el bot."}, "node": {"nodeProperties": "Propiedades de nodo", "actionParameters": "Parámetros de acción", "actionServer": "Ser<PERSON>or de acciones", "actionServerTooltip": "Este es el servidor de acciones en el que se ejecutará la acción", "actionToExecute": "Acción para ejecutar", "actionToRun": "Acción para ejecutar", "actionInstructionParsingError": "Error al analizar las instrucciones: {msg}", "actionArguments": "Llamado con estos argumentos:", "actionNoArguments": "Llamado sin argumentos:", "add": "<PERSON><PERSON><PERSON>", "addAction": "Añadir nueva acción", "nameAlreadyExists": "No puedo cambiar el nodo (Nombre ya existe)", "emptyName": "Nodo nombre no puede estar vacío", "confirmOverwriteParameters": "¿Desea sobrescribir los parámetros existentes?", "contentPaste": "content_paste", "couldNotRetrieveActionServer": "No se pudieron recuperar servidores de acción", "editAction": "Editar acción", "editSkill": "Editar <PERSON>", "end": "Final", "chatbotExecutes": "<PERSON><PERSON><PERSON><PERSON>", "chatbotSays": "Chatbot dice", "triggeredBy": "Se activa el flujo de trabajo", "workflowFails": "El flujo de trabajo falla", "workflowSucceeds": "El flujo de trabajo tiene éxito", "errorInServer": "Parece haber un error en el servidor de Botpress. Póngase en contacto con su administrador.", "errorListingActions": "Error al enumerar las acciones del servidor de acciones", "errorListingActionsMore": "Se ha producido un error al intentar obtener la lista de acciones en el servidor seleccionado", "executeCode": "⚡ Ejecutar código", "finishAddAction": "<PERSON><PERSON><PERSON> acci<PERSON>", "finishUpdateAction": "Acción de actualización", "hasNoParameters": "Esta acción no tiene parámetros", "loadingActionServer": "<PERSON><PERSON><PERSON>, cargando servidores de acción...", "message": "Men<PERSON><PERSON>", "messageToSend": "Mensaje para enviar", "missingLink": "<PERSON><PERSON> perdido", "noActionsFound": "No se han encontrado acciones en este servidor de acciones", "noDescription": "Sin descripción", "nodeName": "Nombre del nodo", "onEnter": "Al entrar", "onReceive": "Al recibir", "return": "Volver", "saySomething": "💬 Decir algo", "theBotWill": "El bot", "transition": {"action": {"endFlow": "<PERSON><PERSON>r flujo", "returnToPreviousFlow": "Volver al flujo anterior", "transitionToNode": "Transición al nodo", "transitionToSubflow": "Transición al subflujo"}, "condition": {"conditionalTransition": "Transicíon condicional", "always": "Siempre", "intentIs": "El intent es {intentName}", "matchesProperty": "<PERSON><PERSON>le la propiedad", "rawExpression": "Expresión sin procesar (avanzada)"}, "edit": "Editar condición para la transición", "expression": "Expresión(ex: !== undefined)", "fieldName": "Nombre del campo(ex: nickname, age)", "javascriptExpression": " Expresión Javascript", "mustSelectSubflow": "Debe seleccionar un subflujo para pasar a", "new": "Nueva condición para la transición", "noSpecific": "Ningún nodo específico", "returnToCallingNode": "Continuar al nodo de llamada", "returnToCallingNodeExecute": "Ejecutar el nodo de llamada otra vez.", "returnToNodeCalled": "Volver al nodo llamado", "specificNodeCalled": "Nodo específico", "showCondition": "Condición", "specifyCondition": "Especifique condición", "whenMetDo": "Cuando se cumpla la condición"}, "transitions": "Transiciones", "unknownParameterType": "`⚠️ Tipo de parámetro desconocido ({type}). Este parámetro se ignorará.", "valuePlaceholder": "Valor", "waitForUserMessage": "Espere el mensaje del usuario", "youCanChangeActions": "Puede cambiar la forma en que se ejecuta la acción Acción proporcionándole parámetros. Algunos parámetros son obligatorios, algunos son opcionales."}, "nodeType": {"action": "Acción", "execute": "Execute", "executeAction": "Acción de ejecución", "listen": "Escucha", "router": "Router", "say": "Decir", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "split": "<PERSON><PERSON><PERSON>", "standard": "<PERSON><PERSON>", "trigger": "detonante"}, "nowSaveAuto": "Pssst! Ahora los flujos se guardan automáticamente, ya no es necesario guardar.", "removeFromLibrary": "<PERSON><PERSON><PERSON> de la libreria", "sessionStartsHere": "Cada sesión de usuario comienza aquí", "setAsStart": "Establecer como nodo inicial", "sidePanel": {"confirmDeleteFlow": "¿Está seguro de que desea eliminar el flujo {name}", "createFlow": "<PERSON><PERSON><PERSON> flujo", "createNewFlow": "Crear nuevo flujo", "createNewTopic": "Create new topic", "duplicateFlow": "<PERSON><PERSON><PERSON> dup<PERSON>", "filterFlows": "Filtrar Flujos", "filterLibrary": "Filtrar libreria", "filterTopicsAndWorkflows": "Filtrar temas y flujos de trabajo", "flowName": "Nombre del flujo", "flowNameHelp": "<PERSON><PERSON><PERSON> puede contener letras, <PERSON><PERSON><PERSON><PERSON>, guiones bajos y guiones. También puede utilizar barras diagonales para crear carpetas (ex: myfolder/mynewflow)", "flowNamePlaceholder": "Elija un nombre para su flujo", "importContent": "Importar contenido", "nameInUse": "Nombre ya en uso", "nameInUseMessage": "Ya existe un flujo con ese nombre. Por favor, elija otro.", "node": "Nodo", "renameFlow": "Cambiar el nombre del flujo", "importTopic": "Importar <PERSON>", "addTopic": "<PERSON><PERSON>", "addWorkflow": "Agregar flujo de trabajo", "tapIconsToAdd": "Toque los iconos de la barra de herramientas para importar o agregar su primer <PERSON><PERSON>.", "renameWorkflow": "Cambiar el nombre del flujo de trabajo", "renameTopic": "Cambiar nombre de Tema", "nameWorkflow": "Nombre de flujo de trabajo ", "nameTopic": "Nombre del Tema"}, "skills": {"couldNotLoad": "No se pudo cargar la vista de habilidades", "edit": "Editar una habilidad", "error": "ERROR – Skill \"{size}\" es un tamaño no válido para la ventana Habilidad. Los tamaños válidos son {sizes}.", "generatingSkillFlow": "Generar tu flujo de habilidades...", "insert": "Inserte una nueva habilidad"}, "toolbar": {"clickDetails": "Haga clic para obtener más detalles", "currentlyEditing": "{name} actualmente está editando este flujo", "missingDetails": "Fal<PERSON> {nb} Enlaces", "problemsWithFlow": "Hay algunos problemas con su flujo.", "renamingAndDeletingDisabled": "Cambiar el nombre y eliminar flujos está deshabilitado", "somebodyIsEditing": "Alguien está editando otro flujo", "whenDiscussionTimeouts": "Cuando se espera un tiempo de espera de discusión (el usuario no responde en el marco temporal configurado) será redirigido aquí.", "whenConversationEnds": "Cuando finalice una conversación (sin transición), será redirigido aquí.", "whenErrorEncountered": "Cuando se encuentra un error en el flujo, el usuario se redirige aquí", "salesCallToAction": "Prueba Enterprise gratis", "salesCallToActionDescription": "Chatbots seguros, escalables y de nivel empresarial"}, "topic": "<PERSON><PERSON>", "topicEditor": {"alreadyExistButDifferent": "Estos elementos ya existen pero son diferentes", "contentOverview": "Resumen de contenido", "couldNotParseFile": "No se pudo analizar el archivo JSON: {msg}", "createNewTopic": "<PERSON>rea un Tema nuevo", "createTopic": "<PERSON><PERSON><PERSON>", "dontExisteWillCreate": "Estos elementos no existen y se crearán", "editTopic": "<PERSON><PERSON>", "importContent": "Importar contenido", "importedSuccessfully": "{detected} importado correctamente!", "noChangesToApply": "No hay cambios para aplicar, o todo en el archivo es idéntico al contenido existente", "overwrite": "Sobrescribir contenido existente", "selectJson": "Seleccione su archivo JSON", "topicName": "Nombre del Tema", "topicNameHelp": "Elija un nombre amplio para representar su Tema, por ejemplo, RR.HH. o TI. Puedes cambiarle el nombre más tarde", "unknownFileType": "Tipo de archivo desconocido o formato no válido", "willBeCreated": "Este elemento no existe y se creará", "willBeOverwritten": "Ya existe un elemento con ese nombre. El contenido se sobrescribirá", "workflowName": "Nombre del flujo de trabajo"}, "topicList": {"bigWarning": "ADVERTENCIA:", "confirmDeleteFlow": "¿Estás seguro de que quieres eliminar este flujo de trabajo?", "confirmDeleteTopic": "¿Estás seguro de que deseas eliminar este Tema? También se eliminarán sus preguntas y respuestas y los flujos de trabajo.", "createNewWorkflow": "Crear nuevo flujo de trabajo", "defaultWorkflows": "Flujos de trabajo predeterminados", "deleteTopic": "<PERSON><PERSON><PERSON>", "deleteWorkflow": "Eliminar flujo de trabajo", "editQna": "Editar Q&A", "editTopic": "<PERSON><PERSON>", "editWorkflow": "Editar flujo de trabajo", "exportTopic": "Exportar Tema", "flowsAssociatedDelete": "Se eliminarán {warning} {count} flujos asociados con el Tema", "importExisting": "Importar flujo de trabajo existente", "nbQuestionsInTopic": "Número de preguntas en ese Tema", "nbTriggersInWorkflow": "Número de activadores de NLU en ese flujo de trabajo", "workflowReceiving": "Flujos de trabajo que hacen referencia a este flujo de trabajo:"}, "transition": "Transición", "unauthUpdate": "Actualización de flujo no autorizada. No tiene privilegios de rol suficientes para modificar flujos.", "workflow": {"create": "<PERSON><PERSON><PERSON> flujo de trabajo", "edit": "Editar flujo de trabajo: {name}", "labelHelp": "La etiqueta es un nombre descriptivo que puede reemplazar el nombre en la lista de temas.", "name": "Nombre del flujo de trabajo"}}, "sideBar": {"config": "Configuración", "content": "Contenido", "flows": "<PERSON><PERSON><PERSON>", "nlu": "Comprensión del lenguaje"}}, "toolbar": {"emulator": "<PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>", "logsPanel": "Panel de Log", "readDoc": "Documentación", "showEmulator": "<PERSON>rar <PERSON>", "toggleLogsPanel": "Mostrar Logs", "toggleEmulator": "Activar Emulador", "toggleSidePanel": "Activar panel lateral"}, "bottomPanel": {"inspector": {"autoExpand": "Expandir automáticamente todos los nodos"}, "closePanel": "Cerrar panel", "logs": {"scrollToFollow": "Desplácese para seguir los registros", "downloadLogs": "Descargar registros", "clearHistory": "Borrar historial de registro", "endOfLogs": "Fin de registros", "debug": {"confUpdated": "¡Configuración de depuración actualizada correctamente!"}, "filter": "filtrar", "botLevelOBS": "OBS: Se considera que un log proviene de un bot si se usa bp.logger.forBot(...)"}, "depurador": {"fetchingEvent": "Obteniendo evento ...", "topIntents": "Intenciones principales", "topPredictions": "Predicciones principales", "displayDebugging": "Mostrar depuración en flujos de trabajo", "autoFocus": "Enfoque automático en mensajes nuevos", "newSession": "Crea una nueva sesión", "splashMessage": "Inicie una conversación con su chatbot y haga clic en cualquier mensaje para inspeccionar su comportamiento.", "unauthorized": "No autorizado", "unauthorizedMessage": "No tiene permisos suficientes para inspeccionar eventos.", "unauthorizedMessage2": "Se requiere permiso: acceso de escritura en \" module.extensions \"", "settings": {"confUpdated": "¡Configuración actualizada correctamente!", "confUpdatedError": "Hubo un error al analizar su configuración. Valide la sintaxis", "payloadSent": "¡Carga útil enviada correctamente!", "payloadSentError": "Hubo un error al analizar su carga útil. Valide la sintaxis", "editConf": "Editar configuración", "editConfHelper": "Prueba los cambios de configuración temporales. Actualiza la página para restablecer.", "editConfPlaceholder": "Cambiar la configuración del chat web (debe ser un json válido)", "saveConf": "Guardar configuración", "sendRawPayloads": "Enviar cargas útiles sin procesar", "sendRawPayloadsHelper": "Envíe cualquier mensaje JSON válido, para probar eventos personalizados, por ejemplo", "sendRawPayloadsPlaceholder": "Carga útil JSON válida", "saveRawPayloads": "Enviar carga <PERSON>", "alwaysShowDebugger": "Mostrar siempre el depurador", "updateDebuggerOnNew": "Actualizar depurador en mensaje nuevo", "userId": "ID de usuario", "userIdHelper": "Cambia el ID de usuario almacenado en su navegador", "userIdPlaceholder": "Su ID de usuario", "authToken": "Token de autenticación externo", "authTokenHelper": "Debe ser un token JWT válido", "authTokenPlaceholder": "Token generado desde su sistema", "save": "<PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON><PERSON>", "basic": "Basica", "advanced": "Avanzada"}, "processing": {"afterMW": "Después de Middleware", "beforeMW": "Antes de Middleware", "received": "Evento recibido", "stateLoaded": "Estado de usuario cargado", "hook": "<PERSON><PERSON><PERSON>", "mw": "Middleware", "dialog": "Diálogo de procesamiento", "action": "Acción", "completed": "Procesamiento de eventos completado", "executedIn": "Ejecutado en {n} ms", "type": "Escribe", "stacktrace": "Stacktrace"}, "actions": {"say": "Decir {x}", "startWorkflow": "Iniciar flujo de trabajo {x}", "goToNode": "Ir al nodo {x}", "redirectTo": "Redirigir a {x}", "continue": "<PERSON><PERSON><PERSON><PERSON>", "cancelPrompt": "<PERSON><PERSON><PERSON> solicitud", "informPrompt": "Informar de solicitud"}, "dialog": {"decision": "Decisión", "suggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flowNodes": "Nodos de flujo", "dialogManager": "Administrador de diálogos"}, "entities": {"type": "Escribe", "source": "Fuente", "normalizedValue": "Valor normalizado"}, "inspector": {"copyEventPath": "Copiar ruta de evento", "expandAll": "Expandir todo"}, "notAvailable": "No disponible", "ndu": {"topTriggers": "Principales activadores", "DecisionsTaken": "Decisiones tomadas", "sendKnowledge": "Envía conocimientos {x}", "startWorkflow": "Iniciar flujo de trabajo {x}", "goToNode": "Ir al nodo {x}", "redirectTo": "Redirigir a {x}", "continueFlowExecution": "Continuar con la ejecución del flujo", "informCurrentPrompt": "Informar al mensaje actual", "cancelCurrentPrompt": "Cancelar el mensaje actual", "noResults": "Sin resultados", "dialogUnderstanding": "Comprensión del diálogo"}, "nlu": {"intentsVeryClose": "Los intentos previstos están muy cerca", "youCanAccountForIt": "Puede contabilizarlo marcando la variable:", "ambiguous": "Ambiguous", "noModel": "No se pudo ejecutar NLU porque no se entrenó ningún modelo", "languageUnderstanding": "Comprensión del lenguaje"}, "slots": {"slot": "Slot", "source": "Fuente", "extracted": "Extraído", "value": "Valor: {x}", "turnsAgo": "{x} turnos atrás", "thisTurn": "<PERSON>ste turno"}, "summary": {"cannotDisplay": "Cannot display event summary", "state": "Expresar", "errors": "Errores"}, "triggers": {"insideTopic": "Tema interno", "outsideTopic": "Tema externo", "insideWorkflow": "InsideWork", "qna": "QnA", "wf": "WF", "node": "Node"}, "eventNotFound": {"title": "Evento no encontrado", "message": "No se encontró el evento solicitado. Posibles motivos:", "message2": "El recopilador de eventos no está habilitado en Botpress Config", "message3": "El evento se eliminó de la base de datos"}}}}