// Copyright 2016 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "../button/common";
@import "./common";

/*
Control groups

Markup:
<div class="#{$ns}-control-group">
  <button class="#{$ns}-button #{$ns}-icon-filter">Filter</button>
  <input type="text" class="#{$ns}-input" placeholder="Find filters..." />
</div>
<div class="#{$ns}-control-group">
  <div class="#{$ns}-select">
    <select>
      <option selected>Filter...</option>
      <option value="1">Issues</option>
      <option value="2">Requests</option>
      <option value="3">Projects</option>
    </select>
  </div>
  <div class="#{$ns}-input-group">
    <span class="#{$ns}-icon #{$ns}-icon-search"></span>
    <input type="text" class="#{$ns}-input" value="from:ggray to:allorca" />
  </div>
</div>
<div class="#{$ns}-control-group">
  <div class="#{$ns}-input-group">
    <span class="#{$ns}-icon #{$ns}-icon-people"></span>
    <input type="text" class="#{$ns}-input" placeholder="Find collaborators..." style="padding-right:94px" />
    <div class="#{$ns}-input-action">
      <button class="#{$ns}-button #{$ns}-minimal #{$ns}-intent-primary">
        can view<span class="#{$ns}-icon-standard #{$ns}-icon-caret-down #{$ns}-align-right"></span>
      </button>
    </div>
  </div>
  <button class="#{$ns}-button #{$ns}-intent-primary">Add</button>
</div>

Styleguide control-group
*/

.#{$ns}-control-group {
  // create a new stacking context to isolate all the z-indices
  @include new-render-layer();
  @include pt-flex-container(row);
  // each child is full height
  align-items: stretch;

  // similarly to button groups, elements in control groups are stacked in a
  // very particular order for best visual results. in each level of selector
  // specificity, we define disabled styles last so that they override all other
  // equally-specific styles (e.g. we don't want mouse interactions or focus
  // changes to change the z-index of a disabled element).

  .#{$ns}-button,
  .#{$ns}-html-select,
  .#{$ns}-input,
  .#{$ns}-select {
    // create a new stacking context
    position: relative;
  }

  .#{$ns}-input {
    z-index: index($control-group-stack, "input-default");
    // inherit radius since it's most likely to be zero
    border-radius: inherit;

    &:focus {
      z-index: index($control-group-stack, "input-focus");
      border-radius: $pt-border-radius;
    }

    &[class*="#{$ns}-intent"] {
      z-index: index($control-group-stack, "intent-input-default");

      &:focus {
        z-index: index($control-group-stack, "intent-input-focus");
      }
    }

    &[readonly],
    &:disabled,
    &.#{$ns}-disabled {
      z-index: index($control-group-stack, "input-disabled");
    }
  }

  .#{$ns}-input-group[class*="#{$ns}-intent"] .#{$ns}-input {
    z-index: index($control-group-stack, "intent-input-default");

    &:focus {
      z-index: index($control-group-stack, "intent-input-focus");
    }
  }

  .#{$ns}-button,
  .#{$ns}-html-select select,
  .#{$ns}-select select {
    @include new-render-layer();
    z-index: index($control-group-stack, "button-default");
    // inherit radius since it's most likely to be zero
    border-radius: inherit;

    &:focus {
      z-index: index($control-group-stack, "button-focus");
    }

    &:hover {
      z-index: index($control-group-stack, "button-hover");
    }

    &:active {
      z-index: index($control-group-stack, "button-active");
    }

    &[readonly],
    &:disabled,
    &.#{$ns}-disabled {
      z-index: index($control-group-stack, "button-disabled");
    }

    &[class*="#{$ns}-intent"] {
      z-index: index($control-group-stack, "intent-button-default");

      &:focus {
        z-index: index($control-group-stack, "intent-button-focus");
      }

      &:hover {
        z-index: index($control-group-stack, "intent-button-hover");
      }

      &:active {
        z-index: index($control-group-stack, "intent-button-active");
      }

      &[readonly],
      &:disabled,
      &.#{$ns}-disabled {
        z-index: index($control-group-stack, "intent-button-disabled");
      }
    }
  }

  // input group contents appear above input always
  .#{$ns}-input-group > .#{$ns}-icon,
  .#{$ns}-input-group > .#{$ns}-button,
  .#{$ns}-input-group > .#{$ns}-input-action {
    z-index: index($control-group-stack, "input-group-children");
  }

  // keep the select-menu carets on top of everything always (particularly when
  // .#{$ns}-selects are focused).
  .#{$ns}-select::after,
  .#{$ns}-html-select::after,
  .#{$ns}-select > .#{$ns}-icon,
  .#{$ns}-html-select > .#{$ns}-icon {
    z-index: index($control-group-stack, "select-caret");
  }

  // have consecutive elements share a border
  &:not(.#{$ns}-vertical) {
    > * {
      margin-right: -$button-border-width;
    }

    .#{$ns}-dark & {
      > * {
        margin-right: 0;
      }

      // consecutive buttons within a button group look okay out of the box, but
      // we need need to make a small correction for non-grouped buttons. this
      // replicates what's already done in dark theme button groups.
      > .#{$ns}-button + .#{$ns}-button {
        margin-left: $button-border-width;
      }
    }
  }

  // Add border radius inheritance to support components wrapped in a popover
  .#{$ns}-popover-wrapper,
  .#{$ns}-popover-target {
    border-radius: inherit;
  }

  // round the left corners of the left-most element
  > :first-child {
    border-radius: $pt-border-radius 0 0 $pt-border-radius;
  }

  // round the right corners of the right-most element
  > :last-child {
    margin-right: 0;
    border-radius: 0 $pt-border-radius $pt-border-radius 0;
  }

  // round all the corners of the only child element
  > :only-child {
    margin-right: 0;
    border-radius: $pt-border-radius;
  }

  // bring back border radius on these buttons
  .#{$ns}-input-group .#{$ns}-button {
    border-radius: $pt-border-radius;
  }

  /*
  Responsive control groups

  Markup:
  <div class="#{$ns}-control-group">
    <div class="#{$ns}-input-group #{$ns}-fill">
      <span class="#{$ns}-icon #{$ns}-icon-people"></span>
      <input type="text" class="#{$ns}-input" placeholder="Find collaborators..." />
    </div>
    <button class="#{$ns}-button #{$ns}-intent-primary">Add</button>
  </div>
  <div class="#{$ns}-control-group #{$ns}-fill">
    <button class="#{$ns}-button #{$ns}-icon-minus #{$ns}-fixed"></button>
    <input type="text" class="#{$ns}-input" placeholder="Enter a value..." />
    <button class="#{$ns}-button #{$ns}-icon-plus #{$ns}-fixed"></button>
  </div>

  Styleguide control-group-fill
  */

  > .#{$ns}-fill {
    flex: 1 1 auto;
  }

  &.#{$ns}-fill > *:not(.#{$ns}-fixed) {
    flex: 1 1 auto;
  }

  /*
  Vertical control groups

  Markup:
  <div class="#{$ns}-control-group #{$ns}-vertical" style="width: 300px;">
    <div class="#{$ns}-input-group #{$ns}-large">
      <span class="#{$ns}-icon #{$ns}-icon-person"></span>
      <input type="text" class="#{$ns}-input" placeholder="Username" />
    </div>
    <div class="#{$ns}-input-group #{$ns}-large">
      <span class="#{$ns}-icon #{$ns}-icon-lock"></span>
      <input type="password" class="#{$ns}-input" placeholder="Password" />
    </div>
    <button class="#{$ns}-button #{$ns}-large #{$ns}-intent-primary">Login</button>
  </div>

  Styleguide control-group-vertical
  */

  &.#{$ns}-vertical {
    flex-direction: column;

    > * {
      margin-top: -$button-border-width;
    }

    > :first-child {
      margin-top: 0;
      border-radius: $pt-border-radius $pt-border-radius 0 0;
    }

    > :last-child {
      border-radius: 0 0 $pt-border-radius $pt-border-radius;
    }
  }
}
