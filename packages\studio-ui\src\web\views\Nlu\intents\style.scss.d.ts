// This file is automatically generated.
// Please do not change this file!
interface CssExports {
  'active': string;
  'buttonTip': string;
  'centerContainer': string;
  'centerElement': string;
  'count': string;
  'ctxSelect': string;
  'entities-list': string;
  'entityItem': string;
  'entityLabel': string;
  'entityNameInput': string;
  'entitySelect': string;
  'entitySelectPopover': string;
  'header': string;
  'hint': string;
  'index': string;
  'intentEditor': string;
  'label-colors-0': string;
  'label-colors-1': string;
  'label-colors-2': string;
  'label-colors-3': string;
  'label-colors-4': string;
  'label-colors-5': string;
  'label-colors-6': string;
  'label-colors-7': string;
  'label-colors-8': string;
  'link': string;
  'liteIntentEditor': string;
  'placeholder': string;
  'selectionText': string;
  'shortcutLabel': string;
  'slotMark': string;
  'slotMenu': string;
  'slotMenuItem': string;
  'slotSidePanel': string;
  'slotsContainer': string;
  'title': string;
  'utterance': string;
  'utterances': string;
  'wrong': string;
}
declare var cssExports: CssExports;
export = cssExports;
