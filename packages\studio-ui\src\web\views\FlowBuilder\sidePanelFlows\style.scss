@import '../../../palette.scss';

.sidePanel {
  position: relative;
  background-color: var(--white);
  height: 100%;
  overflow-y: auto;
  font-size: 12px !important;

  :global {
    .bp3-tab {
      font-size: 12px !important;
    }

    .bp3-editable-text-editing {
      border-radius: 5px;
      height: 20px;
      margin: 5px 5px;
    }

    .bp3-editable-text.bp3-editable-text-editing::before {
      box-shadow: unset;
      border: solid 1px var(--ocean);
    }

    .bp3-editable-text input {
      height: 100% !important;
    }

    .bp3-editable-text.bp3-editable-text-editing {
      width: 100%;
      min-width: 0px !important;
      padding-left: var(--spacing-medium);
      padding-right: var(--spacing-medium);
    }

    .bp3-editable-text:hover::before {
      box-shadow: unset !important;
    }

    .bp3-tree-node.bp3-tree-node-selected > .bp3-tree-node-content {
      color: var(--shark);
      background-color: var(--hover-ocean);
    }

    .bp3-tree-node .bp3-tree-node {
      margin: 0px var(--spacing-medium);

      .bp3-tree-node-content {
        border-radius: 5px;
      }
    }

    .bp3-tree-root > .bp3-tree-node > .bp3-tree-node-content > .bp3-tree-node-icon {
      display: none;
    }

    .bp3-tree-root .bp3-tree-node-list > :last-child {
      :hover {
        background-color: var(--hover-ocean);
      }
      .bp3-icon {
        color: var(--ocean) !important;
      }
    }

    .bp3-tree-node {
      cursor: pointer;
    }

    .bp3-tree-root {
      display: flex;
      flex-direction: column;
      height: 100%;
      margin-bottom: 0px !important;
    }

    .bp3-tree {
      height: 100%;
    }

    .bp3-tree-node-caret-none {
      min-width: 8px;
    }

    .bp3-tree-node-list,
    .bp3-icon {
      color: var(--shark);
    }

    .bp3-elevation-0 {
      box-shadow: unset !important;
    }

    .bp3-editable-text-input {
      width: 100% !important;
    }
  }
}

.addWorkflowNode {
  color: var(--ocean);
}

.tree {
  overflow: auto;
  height: 100%;
}

.treeNode {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  &:hover {
    .overhidden {
      display: block;
    }
  }
}

.overhidden {
  display: none;
}

.mainoverlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: var(--white);
  z-index: 11;
  padding: 15px;
}

.toolPanel {
  padding: 2px;

  .title {
    height: 25px;
    font-weight: bold;
  }

  .section {
    flex-wrap: wrap;
  }
}

.topicName {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag {
  background: var(--c-neutral--light-2);
  border-radius: 3px;
  color: var(--c-background--dark-2);
  font-size: 8px;
  padding: 6px 6px 5px;
}

.toolItem {
  width: 100%;
  margin: 0 3px 3px 0;
  border: 1px solid #ddd;
  display: inline-block;
  font-size: 1.2rem;

  cursor: move; /* fallback if grab cursor is unsupported */
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;

  &:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
  }
}

.grabbable {
  cursor: move; /* fallback if grab cursor is unsupported */
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;

  &:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
  }
}

.tabs {
  width: 100%;
}

.emptyState {
  color: $content-color-light;
  font-size: 14px;
  margin: 20px 0 0;
}

.modalHeader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.referencedWorkflows {
  color: #2458b3;
}

.rightPanel {
  position: absolute;
  width: 0;
}
.rightPanelActive {
  position: relative;
  right: 0;
  min-width: 350px;
  max-width: 50%;
  overflow-x: auto;
}
