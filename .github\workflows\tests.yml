name: Tests
on: [pull_request]
jobs:
  unit:
    name: Unit
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@master
      - uses: actions/setup-node@v2
        with:
          node-version: '12.18.1'
          cache: 'yarn'
      - name: Fetch Node Packages
        run: |
          yarn --immutable
      - name: Run tests
        run: |
          yarn test:unit --coverage --verbose
