.diagram {
  height: 100%;
  flex-shrink: 0;
  flex: auto;
}

.container {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
}

.diagram :global(.srd-diagram) {
  height: 100%;
  background: linear-gradient(90deg, var(--bg) 10px, transparent 1%) center,
    linear-gradient(var(--bg) 10px, transparent 1%) center, #e2e2e2;
  background-position: top left;
  background-size: 12px 12px;
  margin: 0 calc(var(--spacing-x-large) * -1);
}

.wrapper {
  background-color: #f1f1f1;
}
