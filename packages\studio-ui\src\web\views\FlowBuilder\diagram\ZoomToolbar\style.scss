.zoomWrapper {
  background: #fff;
  position: absolute;
  border-radius: 5px;
  bottom: var(--spacing-medium);
  left: var(--spacing-large);
  padding: var(--spacing-small) var(--spacing-medium);

  button {
    background: var(--bg) !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 5px;

    &:disabled {
      opacity: 0.4;
    }

    svg {
      fill: var(--shark) !important;
    }
  }

  .label {
    display: block;
    margin: 0 var(--spacing-medium);
    min-width: 35px;
  }

  label {
    background-color: transparent;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.25;
    text-align: center;
    color: var(--shark);
  }

  select {
    appearance: none;
    cursor: pointer;
    position: absolute;
    left: 28px;
    top: 0;
    width: 56px;
    height: 100%;
    padding: 0 8px;
    background-color: transparent;
    text-align: center;
    line-height: 28px;
    font-size: 15px;
    font-weight: 500;
    opacity: 0;
  }
}

.zoomToFit {
  margin-left: var(--spacing-small);
}

.zoomLevel {
  background: #fff;
}
