.mainLayout {
  align-items: flex-start;
  height: calc(100vh - 28px);
  display: flex;
  flex-wrap: wrap;
}

.container {
  height: 100%;
  flex-grow: 1;
  position: relative;
}

.main {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.mainSplitPaneWToolbar {
  height: auto !important;
  min-height: 0 !important;
  top: 50px !important;
  > [class*='Pane1'] {
    overflow: hidden;
  }
}

:global(.emulator-open) {
  padding-right: var(--emulator-width) !important;
}

.aside {
  background-color: var(--c-background--dark-3);
  overflow-x: hidden;
  transition: var(--t-default);
  width: 4rem;

  &:hover {
    width: 25rem;
  }

  &__logo {
    margin: 1em;
    width: 9rem;
  }

  &-tabs {
    background-color: #ccc;
    margin-left: auto;
    width: 2rem;
  }
}

.main-content {
  color: var(--c-text);
  display: flex;
  grid-area: main;
  overflow: hidden;
  overflow-y: auto;
  position: relative;
}

.sidebar {
  background-color: var(--c-background--dark-1);
  border-left: 1px solid var(--c-neutral);
  width: 2.1rem;
  height: 100%;
  margin-left: auto;

  &-tabs {
    align-items: center;
    display: flex;
    height: 2rem;
    transform: rotate(90deg);
  }
}

.block {
  background: #999;
  width: 250px;
  height: 100%;
  position: absolute;
  right: 20px;
  top: 0;
  overflow-y: scroll;

  &-header {
    height: 2rem;
    padding: 0 var(--s-default);
  }

  &-main {
    padding: var(--s-default);
  }
}

.container :global(.Resizer.horizontal) {
  height: 2px;
  margin: 0 -5px;
  border-top: 2px solid rgba(255, 255, 255, 0.1);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  background: transparent;
  opacity: 0.5;
  z-index: 1;
  background-clip: padding-box;
  cursor: row-resize;

  &:hover {
    -webkit-transition: all 2s ease;
    transition: all 2s ease;
    border-top: 2px solid rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid rgba(0, 0, 0, 0.2);
  }
}
