.summaryTable {
  width: 100%;

  th {
    color: #5c7080; //blueprint gray-1
    font-size: 10px !important;
    padding: 2px;
  }
  td {
    padding: 0px;
    font-size: 11px !important;
  }
}

.hovering {
  width: 100%;

  &:hover {
    cursor: pointer;
  }
}

.header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 var(--spacing-large) var(--spacing-medium);
  position: relative;

  :global {
    .bp3-tab {
      text-transform: uppercase;
      font-weight: bold;
    }

    .bp3-tab {
      height: 23px;
      line-height: 23px;
    }

    .bp3-navbar-group {
      height: 23px;
    }

    .bp3-tab[aria-selected='true'] {
      color: var(--shark) !important;
      border-top: 2px solid var(--ocean);
    }

    .bp3-tab[aria-selected='false'] {
      color: #e3e3e3;
    }

    .bp3-tab {
      background: none;
      border: none;
      border-top: 2px solid transparent;
      display: block;
      color: var(--shark);
      font-size: 12px;
      font-weight: bold;
      line-height: 1.25;
      margin: 0 var(--spacing-x-large) 0 0;
      padding: 0;
      padding-top: var(--spacing-medium);
      transition: border 0.3s;
    }

    .bp3-tab-indicator-wrapper {
      display: none;
    }

    .bp3-tab-indicator {
      background-color: var(--ocean) !important;
    }
  }
}

.splash {
  display: flex;
  align-items: center;
  height: 100%;

  > div {
    text-align: center;
    margin: auto;
    padding: var(--spacing-medium) var(--spacing-large);

    .debuggerIcon {
      display: block;
      margin-bottom: var(--spacing-x-large);
    }

    ul {
      text-align: left;
      padding: 0 0 0 30px;
      font-size: 12px;
      line-height: 1.25;
      text-align: center;
      color: var(--shark);
    }

    p {
      font-size: 12px;
      line-height: 1.25;
      text-align: center;
      color: var(--shark);
    }
  }
}

.notFound {
  > div {
    width: 95%;
  }
}

.content {
  padding: 10px var(--spacing-large);
  height: calc(100% - 50px);
  overflow-y: auto;

  // :global {
  //   .bp3-tab-indicator-wrapper {
  //     display: none;
  //   }

  //   .bp3-tab {
  //     font-weight: bold;
  //     font-size: 12px;
  //     border-bottom: 2px solid transparent;

  //     &[aria-selected='true'] {
  //       color: var(--shark);
  //       border-bottom: 2px solid var(--ocean);
  //     }

  //     &[aria-selected='false'] {
  //       color: var(--seashell);
  //     }
  //   }
  // }

  .tabError {
    color: var(--hover-lighthouse) !important;
    &:hover {
      color: var(--lighthouse) !important;
    }
    &[aria-selected='true'] {
      color: var(--lighthouse) !important;
      border-bottom: 2px solid var(--lighthouse);
    }
  }
}

.inspectorContainer {
  height: 100%;
}

.inspector {
  // overflow-x: hidden;
  background-color: var(--c-background-dark-2);
  // height: 100%;
  //  width: 100%;

  & > ul {
    margin: none;
    padding-left: 5px;

    li {
      position: relative;

      & > label {
        :global(.bp3-icon) {
          cursor: pointer;
          position: absolute;
          left: -5px;
          top: 0px;
          display: none;
        }
      }
    }

    label:hover :global(.bp3-icon) {
      display: unset;
    }
  }
}

:global(.bp3-tooltip .bp3-popover-content) {
  padding: 5px 8px !important;
}

.stacktrace {
  height: 100%;
  width: 100%;
  overflow: auto;
}

.percentBar {
  margin-right: 10px;

  .container {
    text-align: center;
  }

  .barContainer {
    height: 50px;
    width: 12px;
    padding: 2px;
    display: flex;
    border: 1px solid black;

    .bar {
      background-color: blue;
      width: 10px;
      margin-top: auto;
    }
  }
}

.truncate {
  width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.collapsibleContainer {
  border: 1px solid #d8e1e8;
}

.processingItemName {
  cursor: default;
  display: inline-flex;
  align-items: center;
}

.content > .section {
  margin-top: 0;
}

.section {
  margin-top: var(--spacing-large);

  p,
  a {
    color: var(--shark);
    font-size: 12px;
    line-height: 1.25;
    margin: 0;
  }
  ul {
    list-style: none;
    margin: var(--spacing-medium) 0;
    padding: 0 0 0 var(--spacing-medium);
    border-left: 1px solid var(--seashell);
    li {
      color: var(--shark);
      font-size: 12px;
      margin-top: var(--spacing-x-small);
      line-height: 1;
      margin-top: var(--spacing-small);

      &:first-of-type {
        margin-top: 0;
      }
    }
  }
}

.processingItem {
  font-size: 12px;
  color: var(--shark);

  &.processingSection {
    margin-bottom: var(--spacing-medium);

    & + ul {
      margin-top: 0;
    }
  }

  .error {
    color: var(--lighthouse);
    margin: 0 var(--spacing-medium) 0 0;

    svg {
      fill: var(--lighthouse);
    }
  }

  .info {
    color: var(--ocean);
    margin: 0 var(--spacing-medium) 0 0;

    svg {
      fill: var(--ocean);
    }
  }

  svg {
    fill: var(--gray);
  }

  :global(.bp3-icon-small-cross) {
    svg {
      fill: var(--lighthouse);
    }
  }

  .itemButtonIcon {
    margin-right: var(--spacing-small) !important;
  }

  .itemButton {
    background: none;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    margin: 0;
    color: var(--shark);
    transition: color 0.3s;
    text-align: left;

    svg {
      transition: fill 0.3s;
    }
  }

  .expanded {
    display: block;
    width: 100%;
  }

  .infoBox {
    display: block;
    padding: var(--spacing-medium);
    border-radius: 3px;
    margin-top: var(--spacing-medium);
    border: 1px solid var(--seashell);
    font-size: 10px;
    font-weight: 400;
    line-height: 1.5;
    overflow: auto;
    color: var(--shark);
  }

  .time {
    color: var(--gray);
    font-size: 10px;
    line-height: 1.5;
  }
}

.inspecting {
  &:hover {
    border: 2px solid yellow;
  }
}

.bar {
  display: flex;
  border-radius: 3px;
  width: 800px;

  .item {
    height: 13px;
    opacity: 0.8;
    border-right: 1px solid black;
  }

  .error {
    background-color: var(--lighthouse);
  }

  .ok {
    background-color: var(--ocean);
  }
}

.sectionContainer {
  display: flex;
  flex-wrap: wrap;
}
.section {
  // flex-grow: 1;
  min-width: 170px;
}

.slightBold {
  font-weight: 500;
}

.underline {
  text-decoration: underline;
}
