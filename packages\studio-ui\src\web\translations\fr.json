{"config": {"additionalDetails": "Détails supplémentaires", "avatarAndCover": "Avatar et photo de couverture", "avatarUploadSuccess": "L'avatar du bot a été téléchargé avec succès. V<PERSON> devez enregistrer le formulaire pour que les modifications prennent effet.", "botAvatar": "Avatar de bot", "botConfiguration": "Configuration du bot", "chooseFile": "<PERSON><PERSON>", "configUpdated": "La configuration du bot a été mise à jour avec succès", "confirmChangeLanguage": "Voulez-vous vraiment changer la langue de votre bot de {currentName} à {newName} ? Tous vos éléments de contenu seront copiés, assurez-vous de les traduire.", "confirmUnmount": "Voulez-vous vraiment démonter ce bot? Toutes les fonctionnalités de ce bot deviendront indisponibles.", "contactEmail": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "coverPicture": "Image de couverture", "coverUploadSuccess": "L'image de couverture a été téléchargée avec succès. V<PERSON> devez enregistrer le formulaire pour que les modifications prennent effet.", "defaultLanguage": "Langue par défaut", "description": "Description", "formContainsErrors": "Le formulaire contient des erreurs", "language": "<PERSON><PERSON>", "linkToPolicy": "Lien vers les politiques de confidentialité", "linkToTerms": "Lien vers les termes et conditions", "phoneNumber": "Numéro de téléphone", "requireImageFile": "{targetProp} doit être une image", "supportedLanguages": "Langues supportées", "website": "Site web", "botId": "Id du bot", "copyToClipboard": "Copié dans le presse papier"}, "libraries": {"fullName": "Librairies", "addLibrary": "Ajouter cette librairie", "searchNpm": "Chercher une librairie sur NPM (recommandé)", "searchGithub": "Ajouter un répertoire GitHub (peut être lent)", "uploadArchive": "Téléverser une archive", "libraries": "Librairies", "splash": {"text1": "Ces librairies peuvent être utilisées par les actions et les hooks de ce bot.", "text2": "Lorsque vous modifiez le fichier 'package.json' via l'éditeur de code, appuyez sur le bouton ci-dessous pour le synchroniser."}, "viewGithub": "Voir sur Github", "customCommand": "Taper une commande personnalisée", "openConsole": "Ouvrez la console pour voir le résultat de la commande", "bundleDeps": "Empaqueter avec ses dépendances", "addSuccess": "Librairie ajoutée avec succès!", "libraryName": "Nom de la librairie", "confirmRemove": "Êtes-vous sûr de vouloir retirer cette librairie?", "deleteSuccess": "Librairie retirée avec succès", "removeFailure": "Il y a eu une erreur lors du retrait de la librairie. V<PERSON>illez regarder les journaux du serveur pour plus de détails", "bundleError": "Il y a eu une erreur lors de l'empaquetage de la librairie.", "betaWarning": "C'est un module expérmental. L'implémentation peut changer drastiquement à court terme"}, "nlu": {"autoTrain": "Entraînement automatique", "cancelTraining": "<PERSON><PERSON><PERSON><PERSON> l'entraînement", "description": "Appuyer sur + dans le panneau de gauche pour créer une intention.", "entities": {"deleteMessage": "Êtes-vous sûr de vouloir supprimer l'entité \"{entityName}\" ?", "duplicate": "Dupliquer l'entité", "examplesLabel": "Exemples valides", "examplesPlaceholder": "Ajou<PERSON>z des exemples qui respectent votre expression (un exemple par ligne)", "filterPlaceholder": "Filtrer les entités", "fuzzyLabel": "Options de correspondance", "fuzzyTooltip": "Les options de correspondance déterminent la tolérance au niveau de l’orthographe (ex: erreur de frappe) dans les mots de 4 caractères ou plus. Stricte signifie qu'aucune erreur n'est permise.", "loose": "Indulgent", "matchCaseLabel": "Sensible à la casse", "matchCaseTooltip": "Est-ce que votre expression doit respecter la casse?", "matchingError": "Un ou plusieurs exemples ne sont pas valides", "matchingSuccess": "Tous les exemples sont valides", "medium": "<PERSON><PERSON><PERSON>", "nameConflictMessage": "Une entité portant le même nom existe déjà. Veuillez choisir un autre nom.", "nameConflictTitle": "Ce nom est déjà utilisé", "namePlaceholder": "Nom de l'entité", "new": "Nouvel entité", "newOccurrence": "Nouvelle variante", "occurrenceLabel": "<PERSON><PERSON><PERSON>", "occurrencePlaceholder": "<PERSON><PERSON><PERSON> une valeur (ex: Chicago)", "occurrenceTooltip": "Une variante est une valeur que peut prendre votre entité. Chaque variante peut avoir plusieurs synonymes.", "patternInvalid": "Expression régulière invalide", "patternLabel": "Expression régulière", "patternPlaceholder": "Ajouter une expression régulière valide", "rename": "Renommer l'entité", "selectPlaceholder": "Sélectionner des entitiés", "sensitiveLabel": "Contient des données sensibles", "sensitiveTooltip": "Les données sensibles seront remplacées par ** avant d'être enregistrées dans la base de données", "strict": "Stricte", "synonymPlaceholder": "Ajouter des synonymes, séparés par des virgules", "title": "Entités"}, "intents": {"actionErrorMessage": "Impossible de {action} cet intention", "chooseContainerLabel": "Choisissez une intention différente pour cette condition", "contextSelectorCreateMissing": "<PERSON><PERSON><PERSON> \"{query}\"", "contextSelectorPlaceholder": "Sélectionnez un contexte", "contextSelectorTooltip": "Vous pouvez écrire dans la barre de recherche pour ajouter de nouveaux contextes.", "createLabel": "Créer l'intention", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer l'intention \"{intentName}\"?", "exactOnly": "comparaison exacte", "filterPlaceholder": "Filtrer les intentions", "hintExactMatch": "Cette intention ne sera considérée que par {exactOnly}. Pour activer l'apprentissage automatique, ajoutez au minimum {nb} alternatives.", "hintIgnored": "Cette intention sera ignorée, ajoutez des alternatives pour qu'elle soit entraînable.", "hintResilient": "Ajoutez {nb} alternatives pour améliorer la compréhension de cet intention.", "nameDupe": "Une intention avec ce nom existe déjà", "nameLabel": "Nom de l'intention", "namePlaceholder": "Choisissez un nom pour cet intention", "new": "Nouvel intention", "selectIntentLabel": "Choisissez une intention", "selectIntentNoResults": "Aucune intention ne correspond", "summaryPlaceholder": "Résumé de l'intention", "title": "Intentions", "utterancePlaceholder": "Écrivez une alternative"}, "slots": {"createTitle": "C<PERSON>er un paramètre pour votre intention", "deleteMessage": "Êtes-vous sûr de vouloir supprimer ce paramètre, ainsi que toutes les marques dans chaque alternative?", "editTitle": "Modifier un paramètre", "emptyState": "Aucun paramètre n'est défini", "entitiesLabel": "Entités associées", "nameLabel": "Nom du paramètre", "namePlaceholder": "Nom ici", "new": "C<PERSON>er un paramètre", "noSlotsToTag": "La sélection ne peut pas être marquée. Veuillez créer un paramètre d'abord.", "save": "Enregistrer", "tagSelectionLabel": "Marquer la sélection", "tagSelectionMessage": "Sélectionnez un paramètre ou utilisez les chiffres pour choisir un élément", "names": {"amountOfMoney": "Quantité de monnaie", "distance": "distance", "duration": "<PERSON><PERSON><PERSON>", "email": "email", "number": "nombre", "ordinal": "ordinal", "phoneNumber": "numéro de téléphone", "quantity": "quantité", "temperature": "température", "time": "temps", "url": "url", "volume": "volume", "any": "autre"}}, "title": "Compréhension du langage", "trainNow": "Entraîner maintenant"}, "qna": {"addNew": "Ajouter un élément", "answer": "Réponse", "confirmDelete": "Voulez-vous supprimer la question?", "context": {"canTypeToCreate": "Vous pouvez taper dans la barre de sélection pour créer de nouveaux contextes.", "create": "<PERSON><PERSON><PERSON> \"{query}\"", "filterByContexts": "Chercher par contexte", "selectContext": "Sélectionnez le contexte...", "title": "Contextes"}, "contexts": "Contextes", "create": "<PERSON><PERSON>er un nouvel élément", "edit": "Modifier Q&R", "editor": {"andOr": "et / ou", "answers": "Réponses", "botWillSay": "Le bot écrira: ", "checkboxRequired": "La case à cocher de l'action est obligatoire", "duplicatesNotAllowed": "Les questions en double ne sont pas autorisées.", "incorrectRedirection": "Traductions manquantes", "inputsRequred": "Des entrées sont requises.", "missingTranslations": "Missing translations", "node": "<PERSON><PERSON><PERSON>", "pasteQuestionHere": "<PERSON><PERSON><PERSON><PERSON> ou copier-coller vos questions ici, une question par ligne.", "questions": "Questions", "redirectToFlow": "Rediriger vers le flow", "typePressAddAnswer": "Ta<PERSON>z et appuyez sur Entrée pour ajouter une réponse. Utilisez ALT + Entrée pour une nouvelle ligne"}, "exportToJson": "Exporter JSON", "form": {"a": "R", "addAnswerAlternative": "Ajouter une alternative à la réponse", "addContent": "Ajouter un contenu", "addMoreQuestionsPlural": "Rédigez au moins {count} autres questions alternatives pour rendre votre agent conversationnel plus résilient", "addMoreQuestionsSingular": "Rédigez au moins une autre question alternative pour rendre votre agent conversationnel plus résilient", "addOneItemTooltip": "Commencez par ajouter un item", "addQuestion": "Ajouter une question", "addQuestionAlternative": "Ajouter une alternative à la question", "cantBeSaved": "Ne peut être enregistré", "chatbotWillRandomlyChoose": "L'agent conversationnel répondra au hasard parmi ces alternatives", "confirmDeleteQuestion": "Êtes-vous certain de vouloir supprimer cette question? Les questions et réponses alternatives seront aussi supprimé.", "copyIdToClipboard": "Copier le ID dans le presse-papier", "deleteQuestion": "Supprimer la question", "disabledTooltip": "Ce question-réponses ne sera pas affiché aux utilisateurs.", "disableQuestion": "Désactiver la question", "disableRedirection": "Désactiver la redirection", "duplicateAnswer": "Vous avez déjà écrit cette réponse", "duplicateQuestion": "Vous avez déjà écrit cette question", "emptyState": "Appuyez sur le + dans la bar d'outils pour ajouter votre première question.", "enableQuestion": "<PERSON>r la <PERSON>", "enableRedirection": "Activer la redirection", "idCopiedToClipboard": "ID copié dans le presse-papier", "incomplete": "Incomplet", "incompleteTooltip": "Ce question-réponses utilisera uniquement la correspondance exacte.", "missingAnswer": "Si vous laissez le champ réponse vide, cette question sera désactivée.", "noResultsFromFilters": "Aucune question trouvé", "missingQuestion": "Si vous laissez le champ question vide, cette question sera désactivée.", "node": "<PERSON><PERSON><PERSON>", "onlyOneLanguage": "Vous avez seulement une langue", "pickNode": "Choisissez un nœud", "pickWorkflow": "Choisissez un flux de travail", "q": "Q", "quickAddAlternative": "Appuyez sur {shortcut} lorsque dans le champ pour ajouter rapidement une nouvelle alternative", "redirectQuestionTo": "Rediriger la question vers", "redirectToWorkflow": "Rediriger vers un flux de travail", "translate": "<PERSON><PERSON><PERSON><PERSON>", "workflow": "Flux de travail", "writeAtLeastTwoMoreQuestions": "Rédiger au moins 2 questions alternatives pour permettre l'apprentissage de l'agent conversationnel", "writeFirstQuestion": "Rédigez une phrase que votre utilisateur serait suceptible d'écrire pour poser sa question", "writeTheAnswer": "Rédigez la réponse à la question", "writingSameQuestion": "É<PERSON><PERSON>re deux fois la même question désactivera ce question-réponses"}, "fullName": "Q&R", "hint": {"addMoreQuestions": "Ajoutez {moreQuestions} pour rendre votre Q&R plus résilient.", "exactMatchOnly": "correspondances exactes uniquement", "moreQuestions": "{remaining} {remaining, plural, one {question} other {questions}} de plus", "willBeExact": "Ce Q&R utilisera des {exactMatchOnly}. Pour activer l'apprentissage automatique, ajoutez au moins {remaining} {remaining, plural, one {question} other {questions}}", "willBeIgnored": "Cet élément sera ignoré. Ajoutez des questions pour débuter l'entraînement."}, "import": {"analysis": "Analyse", "botContains": "Le bot contient {qnaCount} questions et {cmsCount} éléments de contenu.", "clearQuestionsThenInsert": "Effacer les questions existantes, puis insérer mes nouvelles questions et créer / mettre à jour des éléments de contenu", "clearQuestionsAnalyticsWarning": "Effacer des questions les rendront indisponibles dans le rapport d'analytique", "fileContains": "Votre dossier contient {fileQnaCount} questions et {fileCmsCount} éléments de contenu.", "insertNewQuestions": "Insérer les nouvelles questions de mon fichier et créer / mettre à jour les éléments de contenu associés", "notAbleToExtract": "Nous n'avons pas pu extraire de données de votre fichier. Soit le fichier est vide, soit il ne correspond à aucun format connu.", "selectJson": "Sélectionnez votre fichier JSON", "selectJsonHelp": "Sélectionnez un fichier JSON exporté à partir du module Q&R. Vous verrez un résumé des modifications en cliquant sur Suivant", "uploadFile": "<PERSON><PERSON>léverser un fichier", "uploadStatus": "Statut de téléversement", "uploadSuccessful": "Téléchargement réussi", "whatLikeDo": "Qu'aimeriez-vous faire ?"}, "importJson": "Importer JSON", "missingTranslations": "Traductions manquantes", "noQuestionsYet": "Aucune question n'a encore été ajoutée.", "question": "Question", "redirectsAssociated": "Il existe des redirections associées à ces questions, vous pouvez les visualiser dans le formulaire d'édition", "search": "Chercher une question"}, "status": {"disabled": "Démonté", "private": "Collaborateurs uniquement", "public": "<PERSON><PERSON><PERSON>"}, "statusBar": {"contentLanguage": "Langue du contenu", "switchLang": "Modifier la langue du contenu. Langue courante: {currentLang}", "trainChatbot": "Entraîner le chatbot", "train": "<PERSON><PERSON><PERSON><PERSON>", "training": "Entraînement en cours", "ready": "<PERSON><PERSON><PERSON><PERSON>", "cancelTraining": "Annuler l'entraînement", "trainingPending": "Entraînement en attente", "canceling": "Annulation en cours", "trainingError": "L'agent ne peut pas être entraîné"}, "studio": {"content": {"cloneElements": "<PERSON><PERSON>r les éléments sélectionnés", "confirmDeleteItem": "Voulez-vous vraiment supprimer {count, number} {count, plural, one {item} other {items}}?", "contentType": "Type de contenu", "createNew": "<PERSON><PERSON><PERSON> un nouveau {title}", "currentlySearching": "Type sélectionné", "deleteElements": "Supprimer les éléments sélectionnés", "missingClosingCurlyBrace": "Accolade de fermeture manquante", "import": {"analysis": "Analyse", "clearExisting": "Effacez tous les éléments existants, puis importez ceux de mon fichier", "compareNbElements": "Votre fichier contient {fileCmsCount} éléments de contenu alors que votre bot contient {cmsCount} éléments.", "import": "Importer JSON", "missingContentTypes": "Il manque les types de contenu suivants à votre bot: {types}.", "notAbleToExtractData": "Nous n'avons pas pu extraire de données de votre fichier. Soit le fichier est vide, soit il ne correspond à aucun format connu.", "selectFile": "Sélectionnez votre fichier JSON", "selectFileMore": "Sélectionnez un fichier JSON. Il doit être exporté depuis la page Contenu. Vous verrez un résumé des modifications en cliquant sur Suivant", "updateMissing": "Mettre à jour ou créer des éléments manquants présents dans mon fichier", "upload": "<PERSON><PERSON>léverser un fichier", "uploadStatus": "Statut de téléversement", "whatLikeDo": "Qu'est-ce que vous aimeriez faire?"}, "insertVariable": "Insérer une variable", "mustBeDefaultLang": "L'élément doit d'abord être créé dans la langue par défaut.", "noContent": "Il n'y a pas de contenu ici.", "noContentDefined": "Nous pensons que vous n'avez défini aucun type de contenu.", "noContentYet": "Il n'y a pas encore de contenu. Vous pouvez en créer en utilisant le bouton «Ajouter».", "pleaseReadDoc": "Veuillez {readTheDocs} pour voir comment vous pouvez utiliser cette fonctionnalité", "readTheDocs": "lire la documentation", "searchContent": "Rechercher du contenu", "searchIn": "Selectionner une catégorie", "selectContent": "Sélectionner un élément", "changeCategory": "Changer de caté<PERSON>ie", "sideBar": {"createNew": "Créer nouveau {name}", "createNewContent": "Créer nouveau contenu", "filterByType": "Filtrer par type de contenu", "unregisteredWarning": "Le type de contenu n'est pas enregistré."}, "switchToDefaultLang": "Basculer sur {defaultLang} et commencer à modifier", "usageModal": {"contentUsage": "Utilisation du contenu", "node": "Node"}, "contentTypeWarning": "Veuillez noter que ce type de contenu n'est pris en charge qu'avec {channels}"}, "flow": {"invalidName": "Nom invalide", "invalidCharacters": "Le nom contient des caractères réservés ou invalides.", "zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON>", "zoomToFit": "Ajuster à la taille de l'écran", "flowProperties": "Propriétés du flow", "addNode": "Ajouter une node", "addToLibrary": "Ajouter dans la librairie", "cantDeleteFailure": "Vous ne pouvez pas supprimer le nœud d'échec.", "cantDeleteStart": "Vous ne pouvez pas supprimer le point d'entrée.", "cantDeleteSuccess": "Vous ne pouvez pas supprimer le nœud de réussite.", "chips": "Chips", "prevWorkflow": "Retourner au workflow précédent", "nextWorkflow": "Aller au prochain workflow", "endOfWorkflow": "Fin du workflow", "errorOccurred": "Une erreur est survenue ici", "condition": {"addCondition": "Ajouter condition", "backToList": "Retour à la liste", "chooseElement": "Choisir un élément", "confirmDeleteCondition": "Êtes-vus sûr de supprimer cette condition ?", "editCondition": "Modifier Condition", "editTriggers": "Modifier Déclencheurs", "listenActiveWorkflow": "Écouter dans le workflow actif", "listenActiveWorkflowHelp": "Lorsqu'il est activé, ce déclencheur sera uniquement actif lorsque l'utilisateur sera dans le workflow où il est situé.", "noResults": "Aucun résultat.", "savedAutomatically": "Les modification seront sauvegardées automatiquement", "selectCondition": "Sélectionnez une condition"}, "copiedToBuffer": "Copié dans le presse-papier", "disconnectNode": "Déconnecter le nœud", "editQna": "Modifier Q&R", "errorWhileSaving": "Une erreur s'est produite lors de l'enregistrement, de la suppression ou du changement de nom d'un flow. La dernière modification n'a peut-être pas été enregistrée sur le serveur. Veuillez recharger la page avant de continuer l'édition du flow", "flowWideOnReceives": "{count, plural, one {réception} other {réceptions}} à l'échelle du flow", "flowWideTransitions": "{count, plural, one {transition} other {transitions}} à l'échelle du flow", "filterNodes": "Filtrer les nodes", "highlightByName": "<PERSON><PERSON><PERSON> le nom d'un élément", "logs": {"autoRefresh": "Actualisation automatique", "downloadArchives": "Télécharger l'archive du journal", "loadMore": "Charger plus"}, "module": {"learnMore": "Apprendre plus", "notFound": "Module introuvable", "notProperlyRegistered": "Le module n'est pas correctement enregistré", "tryingToLoad": "Il semble que vous essayez de charger un module qui n'a pas été enregistré. Veuillez vous assurer que le module est enregistré puis redémarrez le bot."}, "node": {"nodeProperties": "Propriétés du node", "actionParameters": "Paramètres d'action", "actionServer": "Serveur d'action", "actionServerTooltip": "Il s'agit du serveur d'action sur lequel l'action sera exécutée", "actionToExecute": "Action à exécuter", "actionToRun": "Action à exécuter", "actionInstructionParsingError": "Erreur lors du formattage des instructions: {msg}", "actionArguments": "Exécuté avec ces arguments:", "actionNoArguments": "Exécuté sans arguments:", "add": "ajouter", "addAction": "Ajouter une nouvelle action", "nameAlreadyExists": "Impossible de renommer le node (Ce nom existe déjà)", "emptyName": "Le nom du node ne peut être vide", "chatbotExecutes": "L'assistant <PERSON>l exécute", "chatbotSays": "L'assistant <PERSON>l dit", "triggeredBy": "Le workflow est is déclenché par", "workflowFails": "Échec du flux de travail", "workflowSucceeds": "Réussite du flux de travail", "confirmOverwriteParameters": "Vou<PERSON><PERSON>-vous remplacer les paramètres existants?", "couldNotRetrieveActionServer": "Impossible de récupérer les serveurs d'actions", "editAction": "Modifier l'action", "editSkill": "Modifier compétence", "end": "Fin", "errorInServer": "Il semble y avoir une erreur sur votre serveur Botpress. Veuillez contacter votre administrateur.", "errorListingActions": "Erreur dans le listage des actions du serveur d'actions", "errorListingActionsMore": "Une erreur s'est produite lors de la tentative d'obtention de la liste des actions sur le serveur sélectionné", "executeCode": "⚡ Exécuter du code", "finishAddAction": "Ajouter action", "finishUpdateAction": "Mettre à jour l'action", "hasNoParameters": "Cette action n'a pas de paramètres", "loadingActionServer": "<PERSON><PERSON><PERSON><PERSON> patienter, chargement des serveurs d'action ...", "message": "Message", "messageToSend": "Message à envoyer", "missingLink": "<PERSON><PERSON> man<PERSON>", "noActionsFound": "Aucune action trouvée sur ce serveur d'actions", "noDescription": "Pas de description", "nodeName": "Nom du node", "onEnter": "Sur Entrée", "onReceive": "Sur réception", "return": "<PERSON><PERSON><PERSON>", "saySomething": "<PERSON><PERSON><PERSON><PERSON> que<PERSON> chose", "theBotWill": "Le bot va", "transition": {"action": {"endFlow": "Finir Flow", "returnToPreviousFlow": "Retour au flow précédent", "transitionToNode": "Transition vers nœud", "transitionToSubflow": "Transition vers subflow"}, "condition": {"conditionalTransition": "Transition Contitionelle", "always": "Toujours", "intentIs": "L'intention est {intentName}", "matchesProperty": "Correspond à la propriété", "rawExpression": "Expression brute (avancé)"}, "edit": "Modifier la condition de transition", "expression": "Expression (ex: !== undefined)", "fieldName": "Nom du champ (ex: surnom, âge)", "javascriptExpression": "Expression Javascript", "mustSelectSubflow": "V<PERSON> de<PERSON> sélectionner un subflow vers lequel effectuer la transition", "new": "Nouvelle condition de transition", "noSpecific": "Pas de nœud spécifique", "returnToCallingNode": "Continuer l'exécution du noeud appelant", "returnToCallingNodeExecute": "Exécuter le noeud appelant à nouveau", "returnToNodeCalled": "Retour au noeud appelé", "specificNodeCalled": "Noeud spécifique", "showCondition": "Condition", "specifyCondition": "Spécifiez une condition", "whenMetDo": "Lorsque la condition est remplie, faire"}, "transitions": "Transitions", "unknownParameterType": "⚠️ Type de paramètre inconnu ({type}). Ce paramètre sera ignoré.", "valuePlaceholder": "<PERSON><PERSON>", "waitForUserMessage": "Attendre le message de l'utilisateur", "youCanChangeActions": "Vous pouvez modifier la façon dont l'action est exécutée en lui fournissant des paramètres. Certains paramètres sont obligatoires, d'autres sont facultatifs."}, "nodeType": {"action": "Action", "execute": "Exécuter", "executeAction": "Exécuter Action", "listen": "Écouter", "router": "Routeur", "say": "Dire", "sendMessage": "Envoyer Message", "split": "Diviser", "standard": "Noeud standard", "trigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nowSaveAuto": "Pssst! Les flows sont désormais enregistrés automatiquement, plus besoin d'enregistrer.", "removeFromLibrary": "Supprimer de la bibliothèque", "sessionStartsHere": "Chaque session utilisateur commence ici", "setAsStart": "Définir comme point d'entrée", "sidePanel": {"confirmDeleteFlow": "Voulez-vous vraiment supprimer le flow {name}", "createFlow": "Créer un flow", "createNewFlow": "Créer nouveau flow", "createNewTopic": "Créer nouveau sujet", "duplicateFlow": "Dupliquer le flow", "filterFlows": "Filtrer les flows", "filterLibrary": "Filtrer la librairie", "filterTopicsAndWorkflows": "Filtrer parmis les sujets et les workflows", "flowName": "Nom du flow", "flowNameHelp": "Il ne peut contenir que des lettres, des chiffres, des traits de soulignement et des tirets. Vous pouvez également utiliser des barres obliques pour créer des dossiers (ex: myfolder/mynewflow)", "flowNamePlaceholder": "Choisissez un nom pour votre flow", "importContent": "Importer contenu", "nameInUse": "Nom déjà utilisé", "nameInUseMessage": "Un flow portant ce nom existe déjà. Veuillez en choisir un autre.", "node": "<PERSON><PERSON><PERSON>", "renameFlow": "Renommer le flow", "importTopic": "Importer sujet", "addTopic": "Ajouter sujet", "addWorkflow": "Ajouter workflow", "tapIconsToAdd": "Appuyez sur les icônes de la barre d'outils pour importer ou ajouter votre premier sujet.", "renameWorkflow": "Renommer workflow", "renameTopic": "Renommer sujet", "nameWorkflow": "Name Workflow", "nameTopic": "Name Topic"}, "skills": {"couldNotLoad": "Impossible de charger la vue des compétences", "edit": "Modifier une compétence", "error": "ERREUR - La compétence \"{size}\" est d'une taille non valide pour la fenêtre de compétence. Les tailles valides sont {sizes}.", "generatingSkillFlow": "Génération de votre flux de compétences...", "insert": "Insérez une nouvelle compétence"}, "toolbar": {"clickDetails": "Cliquez pour plus de détails", "currentlyEditing": "{name} modifie actuellement ce flow", "missingDetails": "{nb} liens manquants", "problemsWithFlow": "Il y a des problèmes avec votre flow.", "renamingAndDeletingDisabled": "Renommer et supprimer des flows est désactivé", "somebodyIsEditing": "Quelqu'un modifie un autre flow", "whenDiscussionTimeouts": "Lors d'un délai d'attente de discussion (l'utilisateur ne répond pas dans le délai configuré), il sera redirigé ici.", "whenConversationEnds": "Lorsqu'une conversation se termine (pas de transition), il sera redirigé ici.", "whenErrorEncountered": "Lorsqu'une erreur est rencontrée dans le flux, l'utilisateur est redirigé ici", "salesCallToAction": "Essayez Entreprise gratuitement", "salesCallToActionDescription": "Chatbots sécurisés, évolutifs et de qualité entreprise"}, "topic": "Sujet", "topicEditor": {"alreadyExistButDifferent": "Ces éléments existent déjà, mais ils sont différents", "contentOverview": "Aperçu du contenu", "couldNotParseFile": "Impossible de lire le fichier JSON: {msg}", "createNewTopic": "Créer un nouveau sujet", "createTopic": "C<PERSON>er un sujet", "dontExisteWillCreate": "Ces éléments n'existent pas et seront créés", "editTopic": "Modifier le sujet", "importContent": "Importer du contenu", "importedSuccessfully": "{detected} importé avec succès!", "noChangesToApply": "Il n'y a aucun changement à appliquer, ou tout les éléments sont identiques au contenu existant", "overwrite": "Écraser le contenu existant", "selectJson": "Sélectionnez votre fichier JSON", "topicName": "Nom du sujet", "topicNameHelp": "Choisissez un nom général pour représenter votre sujet, par exemple RH ou TI. Vous pourrez le renommer plus tard.", "unknownFileType": "Type de fichier inconnu ou format non valide", "willBeCreated": "Cet élément n'existe pas et sera créé", "willBeOverwritten": "Un élément portant ce nom existe déjà. Le contenu sera écrasé", "workflowName": "Nom du workflow"}, "topicList": {"bigWarning": "ATTENTION:", "confirmDeleteFlow": "Souhaitez-vous vraiment supprimer ce workflow?", "confirmDeleteTopic": "Souhaitez-vous vraiment supprimer ce sujet? Ses questions-réponses et ses workflows seront également supprimés.", "createNewWorkflow": "Créer un nouveau workflow", "defaultWorkflows": "Workflows par défaut", "deleteTopic": "Supprimer le sujet", "deleteWorkflow": "Supprimer le workflow", "editQna": "Modifier Q&R", "editTopic": "Modifier le sujet", "editWorkflow": "Modifier le workflow", "exportTopic": "Exporter le sujet", "flowsAssociatedDelete": "{warning} {count} flows associés au sujet seront supprimés", "importExisting": "Importer un workflow existant", "nbQuestionsInTopic": "Nombre de questions sur ce sujet", "nbTriggersInWorkflow": "Nombre de déclencheurs sur ce workflow", "workflowReceiving": "Workflows référençant ce workflow:"}, "transition": "Transition", "unauthUpdate": "Mise à jour de flow non autorisée. Vous ne disposez pas des privilèges de rôle suffisants pour modifier les flows.", "workflow": {"create": "C<PERSON>er workflow", "edit": "Modifier le workflow - {name}", "labelHelp": "L'étiquette est un nom convivial qui remplace le nom dans la liste des sujets", "name": "Nom du workflow"}}, "sideBar": {"config": "Configuration", "content": "Contenu", "flows": "Flows", "nlu": "Compréhension du langage naturel"}}, "toolbar": {"emulator": "É<PERSON><PERSON><PERSON>", "help": "Aide", "bottomPanel": "Panneau inférieur", "readDoc": "Lire la documentation", "showEmulator": "Afficher l'émulateur", "toggleBottomPanel": "Basculer le panneau inférieur", "toggleEmulator": "Basculer l'émulateur", "toggleSidePanel": "Basculer le panneau latéral"}, "bottomPanel": {"inspector": {"autoExpand": "Automatiquement expandre les noeuds"}, "closePanel": "<PERSON><PERSON><PERSON> le pannea<PERSON>", "logs": {"scrollToFollow": "Su<PERSON><PERSON> les journaux", "downloadLogs": "Télécharger", "clearHistory": "Effacer l'historique", "endOfLogs": "Fin des journaux", "debug": {"confUpdated": "La configuration de débogage a été mise à jour avec succès!"}, "filter": "filtre", "botLevelOBS": "OBS : Un log est considéré comme provenant d'un bot si bp.logger.forBot(...) est utilisé"}, "debugger": {"fetchingEvent": "Récupération de l'événement...", "topIntents": "Principales intentions", "topPredictions": "Principales prédictions", "displayDebugging": "Afficher débobage dans les workflows", "autoFocus": "Auto-focus sur les nouveaux messages", "newSession": "<PERSON><PERSON><PERSON> une nouvelle session", "splashMessage": "Engagez la conversation avec votre chatbot et cliquez sur n'importe quel message pour inspecter ses comportements.", "unauthorized": "Non autorisé", "unauthorizedMessage": "Vous ne disposez pas des autorisations suffisantes pour inspecter les événements.", "unauthorizedMessage2": "Autorisation requise: accès en écriture sur \"module.extensions\"", "settings": {"confUpdated": "Configuration mise à jour avec succès!", "confUpdatedError": "Une erreur s'est produite lors de l'analyse de votre configuration. Veuillez valider la syntaxe", "payloadSent": "Données envoyées avec succès!", "payloadSentError": "Une erreur s'est produite lors de l'analyse de vos données. Veuillez valider la syntaxe", "editConf": "Modifier la configuration", "editConfHelper": "Testez les modifications de configuration temporaires. Actualisez la page pour réinitialiser.", "editConfPlaceholder": "Modifier les paramètres du Webchat (doit être json valide)", "saveConf": "Enregistrer la configuration", "sendRawPayloads": "Envoyer des données brutes", "sendRawPayloadsHelper": "Envoyez n'importe quel message JSON valide, pour tester des événements personnalisés, par exemple", "sendRawPayloadsPlaceholder": "Données JSON valides", "saveRawPayloads": "Envoyer des données", "alwaysShowDebugger": "Tou<PERSON><PERSON> afficher le débogueur", "updateDebuggerOnNew": "Mettre à jour le débogueur sur un nouveau message", "userId": "ID Utilisateur", "userIdHelper": "Modifie l'ID utilisateur stocké sur votre navigateur", "userIdPlaceholder": "Votre ID d'utilisateur", "authToken": "Jeton d'authentification externe", "authTokenHelper": "Il doit s'agir d'un jeton JWT valide", "authTokenPlaceholder": "Jeton généré à partir de votre système", "save": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Paramètres", "basic": "Élémentaire", "advanced": "<PERSON><PERSON><PERSON>"}, "processing": {"afterMW": "<PERSON><PERSON> le Middleware", "beforeMW": "Avant le Middleware", "received": "Événement Reçu", "stateLoaded": "Chargement Données Utilisateur", "hook": "Hook", "mw": "Middleware", "dialog": "Exécution du dialogue", "action": "Action", "completed": "Traitement complété", "executedIn": "Exécuté en {n} ms", "type": "Type", "stacktrace": "Stacktrace", "timedOut": "La requête a expiré"}, "actions": {"say": "Dire {x}", "startWorkflow": "Commencer workflow {x}", "goToNode": "<PERSON>er au noeud {x}", "redirectTo": "Rediriger vers {x}", "continue": "<PERSON><PERSON><PERSON>", "cancelPrompt": "Annuler Prompt", "informPrompt": "Informer Prompt"}, "dialog": {"decision": "Décision", "suggestions": "Suggestions", "flowNodes": "Noeuds de flux", "dialogManager": "Gestionnaire de dialogue"}, "entities": {"type": "Type", "source": "Source", "normalizedValue": "Valeur normalisée"}, "inspector": {"copyEventPath": "<PERSON><PERSON><PERSON> le chemin de l'événement", "expandAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout"}, "notAvailable": "Non disponible", "ndu": {"topTriggers": "Top déclencheurs", "decisionsTaken": "Décisions prises", "sendKnowledge": "Envoyer connaissances {x}", "startWorkflow": "Démarrer workflow {x}", "goToNode": "<PERSON>er au noeud {x}", "redirectTo": "Rediriger vers {x}", "continueFlowExecution": "Continuer l'exécution du flux", "informCurrentPrompt": "Informer le prompt actuel", "cancelCurrentPrompt": "Annuler le prompt actuel", "noResults": "Aucun résultat", "dialogUnderstanding": "Compréhension du dialogue"}, "nlu": {"intentsVeryClose": "Les intentions prévues sont très proches.", "youCanAccountForIt": "Vous pouvez en rendre compte en vérifiant la variable : ", "ambiguous": "Ambigu", "noModel": "Échec d'évaluation NLU car aucun modèle n'est disponible", "languageUnderstanding": "Compréhension du langage"}, "slots": {"slot": "Slot", "source": "Source", "extracted": "Extraction", "value": "Valeur: {x}", "turnsAgo": "Il y a {x} tours", "thisTurn": "Ce tour"}, "summary": {"cannotDisplay": "Impossible d'afficher le résumé de l'événement", "state": "État", "errors": "<PERSON><PERSON><PERSON>"}, "triggers": {"insideTopic": "Intérieur du sujet", "outsideTopic": "Extérieur du sujet", "insideWorkflow": "Dans le workflow", "qna": "Q&R", "wf": "WF", "node": "Noeud"}, "eventNotFound": {"title": "Événement introuvable", "message": "L'événement demandé est introuvable. Raisons possibles:", "message2": "Le collecteur d'événements n'est pas activé dans Botpress Config", "message3": "L'événement a été supprimé de la base de données"}}}}