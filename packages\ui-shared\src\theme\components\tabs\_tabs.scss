// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "../../common/mixins";

/*
Tabs

Markup:
<div class="#{$ns}-tabs">
    <ul class="#{$ns}-tab-list {{.modifier}}" role="tablist">
        <li class="#{$ns}-tab" role="tab" aria-selected="true">Selected tab</li>
        <li class="#{$ns}-tab" role="tab">Another tab</li>
        <li class="#{$ns}-tab" role="tab" aria-disabled="true">Disabled tab</li>
    </ul>
    <div class="#{$ns}-tab-panel" role="tabpanel">Selected panel</div>
    <div class="#{$ns}-tab-panel" role="tabpanel" aria-hidden="true">Another panel</div>
    <div class="#{$ns}-tab-panel" role="tabpanel" aria-hidden="true">Disabled panel</div>
</div>

.#{$ns}-large - Large tabs

Styleguide tabs
*/

$tab-color-selected: $pt-link-color !default;
$dark-tab-color-selected: $pt-dark-link-color !default;

$tab-indicator-width: 3px !default;

.#{$ns}-tabs.#{$ns}-vertical {
  display: flex;

  // include '>' to ensure we're only modifying
  // these tabs, not tabs that might be further
  // down the DOM hierarchy (i.e. tabs in tabs)
  > .#{$ns}-tab-list {
    flex-direction: column;
    align-items: flex-start;

    .#{$ns}-tab {
      border-radius: $pt-border-radius;
      width: 100%;
      padding: 0 $pt-grid-size;

      &[aria-selected="true"] {
        box-shadow: none;
        background-color: rgba($pt-intent-primary, 0.2);
      }
    }

    .#{$ns}-tab-indicator-wrapper .#{$ns}-tab-indicator {
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      border-radius: $pt-border-radius;
      background-color: rgba($pt-intent-primary, 0.2);
      height: auto;
    }
  }

  // same consideration here: avoid styling any
  // other tabs that might be contained in this
  // vertical tab component
  > .#{$ns}-tab-panel {
    margin-top: 0;
    padding-left: $pt-grid-size * 2;
  }
}

.#{$ns}-tab-list {
  display: flex;
  flex: 0 0 auto;
  align-items: flex-end;
  position: relative;
  margin: 0;
  border: none;
  padding: 0;
  list-style: none;

  // this is fine.
  // stylelint-disable-next-line selector-no-universal
  > *:not(:last-child) {
    margin-right: $pt-grid-size * 2;
  }
}

.#{$ns}-tab {
  @include overflow-ellipsis();
  flex: 0 0 auto;
  position: relative;
  cursor: pointer;
  max-width: 100%;
  vertical-align: top;
  line-height: $pt-button-height;
  color: $pt-text-color;
  font-size: $pt-font-size;

  // support for links in tab titles #363
  a {
    display: block;
    text-decoration: none;
    color: inherit;
  }

  .#{$ns}-tab-indicator-wrapper ~ & {
    // these properties are only for static markup, therefore
    // we never want them if there's a tab indicator
    // stylelint-disable declaration-no-important
    box-shadow: none !important;
    background-color: transparent !important;
    // stylelint-enable declaration-no-important
  }

  &[aria-disabled="true"] {
    cursor: not-allowed;
    color: $pt-text-color-disabled;
  }

  &[aria-selected="true"] {
    border-radius: 0;
    box-shadow: inset 0 (-$tab-indicator-width) 0 $tab-color-selected;
  }

  &[aria-selected="true"],
  &:not([aria-disabled="true"]):hover {
    color: $tab-color-selected;
  }

  &:focus {
    -moz-outline-radius: 0;
  }

  .#{$ns}-large > & {
    line-height: $pt-button-height-large;
    font-size: $pt-font-size-large;
  }
}

.#{$ns}-tab-panel {
  margin-top: $pt-grid-size * 2;

  &[aria-hidden="true"] {
    display: none;
  }
}

.#{$ns}-tab-indicator-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  transform: translateX(0), translateY(0);
  transition: height, transform, width;
  transition-duration: $pt-transition-duration * 2;
  transition-timing-function: $pt-transition-ease;
  pointer-events: none;

  .#{$ns}-tab-indicator {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: $tab-color-selected;
    height: $tab-indicator-width;
  }

  &.#{$ns}-no-animation {
    transition: none;
  }
}

.#{$ns}-dark {
  .#{$ns}-tab {
    color: $pt-dark-text-color;

    &[aria-disabled="true"] {
      color: $pt-dark-text-color-disabled;
    }

    &[aria-selected="true"] {
      box-shadow: inset 0 (-$tab-indicator-width) 0 $dark-tab-color-selected;
    }

    &[aria-selected="true"],
    &:not([aria-disabled="true"]):hover {
      color: $dark-tab-color-selected;
    }
  }

  .#{$ns}-tab-indicator {
    background-color: $dark-tab-color-selected;
  }
}

.#{$ns}-flex-expander {
  flex: 1 1;
}
