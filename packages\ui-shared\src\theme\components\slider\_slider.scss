// Copyright 2016 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../tooltip/common";
@import "./common";

$handle-size: $pt-icon-size-standard !default;
$track-size: $handle-size - $pt-grid-size !default;
$label-offset: $handle-size + 4px !default;

// retain legacy variable aliases so as to not break consumers
$handle-height: $handle-size !default;
$track-height: $track-size !default;

.#{$ns}-slider {
  @include slider-orientation($pt-input-height-large, $vertical: false);
  position: relative;
  outline: none;
  cursor: default;
  user-select: none;

  &:hover {
    cursor: pointer;
  }

  &:active {
    cursor: grabbing;
  }

  &.#{$ns}-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.#{$ns}-slider-unlabeled {
    height: $handle-size;
  }
}

.#{$ns}-slider-track,
.#{$ns}-slider-progress {
  @include slider-track-orientation($handle-size, $track-size, $vertical: false);
  position: absolute;
}

.#{$ns}-slider-track {
  border-radius: $pt-border-radius;
  overflow: hidden;
}

.#{$ns}-slider-progress {
  background: rgba($gray1, 0.2);

  .#{$ns}-dark & {
    background: rgba($black, 0.5);
  }

  @each $intent, $color in $pt-intent-colors {
    &.#{$ns}-intent-#{$intent} {
      background-color: $color;
    }
  }
}

.#{$ns}-slider-handle {
  @include pt-button();
  position: absolute;
  top: 0;
  left: 0;
  border-radius: $pt-border-radius;
  box-shadow: $button-box-shadow-overlay;
  cursor: pointer;
  width: $handle-size;
  height: $handle-size;

  // ensure target handle's label always covers neighbors
  &:focus {
    z-index: 1;
  }

  &:hover {
    @include pt-button-hover();
    z-index: 2;
    box-shadow: $button-box-shadow-overlay;
    cursor: grab;
  }

  &.#{$ns}-active {
    @include pt-button-active();
    box-shadow: $button-box-shadow-overlay-active;
    cursor: grabbing;
  }

  .#{$ns}-disabled & {
    box-shadow: none;
    background: $gray5;
    // easy way to avoid lots of special cases to ignore mouse states when disabled:
    pointer-events: none;
  }

  .#{$ns}-dark & {
    @include pt-dark-button();

    &,
    &:hover {
      background-color: $dark-gray5;
    }

    &.#{$ns}-active {
      background-color: $dark-gray3;
    }
  }

  .#{$ns}-dark .#{$ns}-disabled & {
    border-color: $gray1;
    box-shadow: none;
    background: $gray1;
  }

  .#{$ns}-slider-label {
    margin-left: $handle-size / 2;
    border-radius: $pt-border-radius;
    box-shadow: $pt-tooltip-box-shadow;
    background: $tooltip-background-color;
    color: $tooltip-text-color;

    .#{$ns}-dark & {
      box-shadow: $pt-dark-tooltip-box-shadow;
      background: $dark-tooltip-background-color;
      color: $dark-tooltip-text-color;
    }

    .#{$ns}-disabled & {
      box-shadow: none;
    }
  }

  &.#{$ns}-start,
  &.#{$ns}-end {
    width: $handle-size / 2;
  }

  &.#{$ns}-start {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  &.#{$ns}-end {
    margin-left: $handle-size / 2;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;

    .#{$ns}-slider-label {
      margin-left: 0;
    }
  }
}

.#{$ns}-slider-label {
  @include slider-label-orientation($label-offset);
  display: inline-block;
  position: absolute;
  padding: ($pt-grid-size / 5) ($pt-grid-size / 2);
  vertical-align: top;
  line-height: 1;
  font-size: $pt-font-size-small;
}

.#{$ns}-slider.#{$ns}-vertical {
  @include slider-orientation($pt-input-height-large, $vertical: true);

  .#{$ns}-slider-track,
  .#{$ns}-slider-progress {
    @include slider-track-orientation($handle-size, $track-size, $vertical: true);
  }

  .#{$ns}-slider-progress {
    top: auto;
  }

  .#{$ns}-slider-label {
    @include slider-label-orientation($label-offset, $vertical: true);
  }

  .#{$ns}-slider-handle {
    top: auto;

    .#{$ns}-slider-label {
      margin-top: -$handle-size / 2;
      margin-left: 0;
    }

    &.#{$ns}-end,
    &.#{$ns}-start {
      margin-left: 0;
      width: $handle-size;
      height: $handle-size / 2;
    }

    &.#{$ns}-start {
      border-top-left-radius: 0;
      border-bottom-right-radius: $pt-border-radius;

      .#{$ns}-slider-label {
        transform: translate($label-offset);
      }
    }

    &.#{$ns}-end {
      margin-bottom: $handle-size / 2;
      border-top-left-radius: $pt-border-radius;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}
