// This file is automatically generated.
// Please do not change this file!
interface CssExports {
  'action': string;
  'active': string;
  'button': string;
  'content': string;
  'contentImgWrapper': string;
  'contentWrapper': string;
  'contentsWrapper': string;
  'danger': string;
  'debugInfo': string;
  'error': string;
  'errorIcon': string;
  'failure': string;
  'hasError': string;
  'hasJoinLabel': string;
  'headerWrapper': string;
  'hidden': string;
  'highlighted': string;
  'img': string;
  'in': string;
  'joinLabel': string;
  'large': string;
  'nodeWrapper': string;
  'out': string;
  'outRouting': string;
  'promptPortContent': string;
  'readOnly': string;
  'results': string;
  'rtl': string;
  'secondaryText': string;
  'skill-call': string;
  'small': string;
  'smallButton': string;
  'standard': string;
  'sub-workflow': string;
  'success': string;
  'text': string;
  'textWrapper': string;
  'total': string;
}
declare var cssExports: CssExports;
export = cssExports;
