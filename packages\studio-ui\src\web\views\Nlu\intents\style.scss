.ctxSelect {
  > :first-child {
    min-width: 250px;
    max-width: 350px;
  }
}

.intentEditor {
  display: grid;
  grid-template-columns: minmax(0, 1fr) 200px;

  &:not(.liteIntentEditor) > div:first-child {
    padding: 10px;
  }
}

.header {
  display: grid;
  gap: 10px;
  margin: 10px 0;

  .hint {
    color: #738694;
    margin: 0;
  }

  @media (min-width: 1025px) {
    grid-auto-flow: column;
    align-items: end;
    justify-content: space-between;
  }
}

.utterances > div > p {
  position: relative;
}

.utterances p {
  margin-bottom: 0;
}

.placeholder {
  color: silver;
  position: absolute;
  left: 50px;
  top: 0px;
  z-index: 0;
  user-select: none;
  pointer-events: none;
}

.entities-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
}

p.utterance {
  font-size: 16px;
  line-height: 25px;
  --index-width: 20px;
  --index-margin-right: 5px;
  --hanging-indent: calc(var(--index-width) + var(--index-margin-right));
  padding-left: var(--hanging-indent);
  text-indent: calc(-1 * var(--hanging-indent));

  &.title {
    font-weight: bold;
    line-height: 40px;
    font-size: 25px;
  }
  &.active {
    background-color: #f6f6f6;
  }
  &.wrong {
    color: red;
  }

  .index {
    display: inline-block;
    width: var(--index-width);
    margin-right: var(--index-margin-right);
    text-indent: 0;
    opacity: 0.5;
    text-align: center;
    user-select: none;

    &.count {
      width: inherit;
      background: lightgrey;
      padding: 0 5px;
      border-radius: 5px;
    }
  }

  // Fix duplicate placeholder
  [data-slate-leaf='true'] > span > [contenteditable='false'] + span > [contenteditable='false'],
  [data-slate-leaf='true']
    > span
    > [contenteditable='false']
    + span
    > [contenteditable='false']
    + span
    > [contenteditable='false'] {
    display: none !important;
  }
}

.slotMenu {
  position: absolute;
  display: block;
  z-index: 1000;
  background-color: #fefefe;
  padding: 10px;
  border: solid 1px #ccc;
  border-radius: 5px;
  box-shadow: 0px 3px 8px -2px rgba(0, 0, 0, 0.2);
  p:first-child {
    font-weight: bold;
  }
}

.slotMenuItem {
  margin: 0 5px 5px 0;
}

.slotMark {
  cursor: pointer;
  text-indent: 0;
  transform: translateY(-1px);
  &:hover {
    font-weight: bold;
  }
}

/* ---------- */
/* SLOTS COLORING*/
/* ---------- */

.label-colors-0 {
  background-color: rgb(110, 96, 160);
  color: white;

  &:hover {
    background-color: rgb(104, 97, 129);
  }
}

.label-colors-1 {
  background-color: #6699cc;
  color: white;

  &:hover {
    background-color: darken(#6699cc, 10%);
  }
}

.label-colors-2 {
  background-color: #a23e48;
  color: white;

  &:hover {
    background-color: darken(#a23e48, 10%);
  }
}

.label-colors-3 {
  background-color: #fff275;
  color: black;

  &:hover {
    background-color: darken(#fff275, 10%);
  }
}

.label-colors-4 {
  background-color: teal;
  color: white;

  &:hover {
    background-color: #009d9d;
  }
}

.label-colors-5 {
  background-color: #ff8c42;
  color: white;

  &:hover {
    background-color: darken(#ff8c42, 10%);
  }
}

.label-colors-6 {
  background-color: rgb(185, 97, 126);
  color: white;

  &:hover {
    background-color: rgb(170, 87, 115);
  }
}

.label-colors-7 {
  background-color: rgb(211, 162, 72);
  color: white;

  &:hover {
    background-color: rgb(168, 126, 47);
  }
}

.label-colors-8 {
  background-color: #86bbd8;
  color: black;

  &:hover {
    background-color: darken(#86bbd8, 10%);
  }
}

/* ---------- */
/* SLOTS COLORING*/
/* ---------- */

.slotSidePanel {
  max-width: 300px;
  border-left: solid 1px #eee;
  flex-grow: 0.2;
  overflow: auto;
  background-color: white;
}

.centerContainer {
  position: relative;
  height: calc(100vh - 200px);
  width: 100%;
}

.link {
  cursor: pointer;
}

.centerElement {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  max-width: 400px;
  transform: translate(-50%, -50%);

  button {
    margin-top: 20px;
  }
}

h1,
h2,
h3,
h4 {
  .selectionText {
    color: blue;
    font-style: italic;
  }
}

.buttonTip {
  text-align: center;
  margin-top: 7px;
  font-size: 80%;
  color: #888;
}

.entityNameInput {
  height: 36px;
  padding: 7px;
  font-size: 16px;
  width: 100%;
}

.slotsContainer {
  width: 100%;
  padding: 10px;
  margin-top: 20px;
  ul {
    list-style: none;
    padding: 0;
  }

  li {
    padding: 5px;
  }

  h3 > button {
    float: right;
  }
}

.entityLabel {
  height: 40px;
  cursor: pointer;
  padding: 10px;
  font-size: 16px;
  border-radius: 2px;
}

.shortcutLabel {
  height: 40px;
  padding: 10px;
  font-size: 12px;
  color: white;
  border-left: 4px solid white;

  &.active {
    border-left: 4px solid black;
    color: black;
  }
}

.entityItem {
  a {
    display: none;
    margin-left: 10px;
    color: #888;
    text-decoration: underline;
  }

  &:hover {
    background: #eee;

    a {
      display: initial;
    }
  }
}

.entitySelect {
  width: 100%;
}

.entitySelectPopover {
  max-height: 250px;
  overflow: scroll;
}
