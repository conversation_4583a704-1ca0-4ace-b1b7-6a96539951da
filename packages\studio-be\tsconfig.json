{"compilerOptions": {"declaration": true, "sourceMap": true, "strict": true, "module": "commonjs", "esModuleInterop": true, "resolveJsonModule": true, "target": "es2019", "noImplicitAny": false, "moduleResolution": "node", "experimentalDecorators": true, "useUnknownInCatchVariables": false, "baseUrl": "./src", "outDir": "./out", "typeRoots": ["./src/typings", "./node_modules/@types", "../../node_modules/@types"], "paths": {"botpress/sdk": ["./sdk/botpress.d.ts"]}, "types": ["reflect-metadata", "jest"]}, "include": ["src"], "exclude": ["**/*.test.ts"]}