name: Codestyle
on: [pull_request]
jobs:
  prettier:
    name: <PERSON> Prettier on codebase
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@master
      - uses: actions/setup-node@v2
        with:
          node-version: '12.18.1'
          cache: 'yarn'
      - name: Install Dependencies
        run: |
          yarn --immutable
      - name: Run Prettier
        run: |
          yarn prettier
  eslint:
    name: Run ESLint on codebase
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@master
      - uses: actions/setup-node@v2
        with:
          node-version: '12.18.1'
          cache: 'yarn'
      - name: Install Dependencies
        run: |
          yarn --immutable
      - name: Run ESLint
        run: |
          yarn eslint
