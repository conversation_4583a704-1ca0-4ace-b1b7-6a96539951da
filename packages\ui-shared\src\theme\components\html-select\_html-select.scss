// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";
@import "../popover/common";
@import "./common";

/*
HTML select

Markup:
<div class="#{$ns}-select {{.modifier}}">
  <select {{:modifier}}>
    <option selected>Choose an item...</option>
    <option value="1">One</option>
    <option value="2">Two</option>
    <option value="3">Three</option>
    <option value="4">Four</option>
  </select>
</div>

:disabled - Disabled. Also add <code>.#{$ns}-disabled</code> to <code>.#{$ns}-select</code> for icon coloring (not shown below).
.#{$ns}-fill - Expand to fill parent container
.#{$ns}-large - Large size
.#{$ns}-minimal - Minimal appearance

Styleguide select
*/

.#{$ns}-html-select,
.#{$ns}-select {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  letter-spacing: normal;

  select {
    @extend %pt-select;

    &:disabled {
      @extend %pt-select-disabled;
    }

    &::-ms-expand {
      // IE11 styling to hide the arrow
      display: none;
    }
  }

  .#{$ns}-icon {
    @extend %pt-select-arrow;
    @include pt-icon-colors();
  }

  &.#{$ns}-minimal select {
    @extend %pt-select-minimal;
  }

  &.#{$ns}-large {
    select {
      @extend %pt-select-large;
    }

    &::after, // CSS support
    .#{$ns}-icon {
      top: ($pt-button-height-large - $pt-icon-size-standard) / 2;
      right: $pt-grid-size * 1.2;
    }
  }

  &.#{$ns}-fill {
    &,
    select {
      width: 100%;
    }
  }

  .#{$ns}-dark & {
    select {
      @extend %pt-dark-select;
    }

    option {
      background-color: $dark-popover-background-color;
      color: $pt-dark-text-color;
    }

    option:disabled {
      color: $pt-dark-text-color-disabled;
    }

    &::after {
      color: $pt-dark-icon-color;
    }
  }
}

.#{$ns}-select {
  &::after {
    @extend %pt-select-arrow;
    @include pt-icon();
    content: $pt-icon-double-caret-vertical;
  }
}
