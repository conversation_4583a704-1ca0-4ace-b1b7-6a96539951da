{"name": "@botpress/studio", "version": "0.0.68", "description": "Create your virtual assistants", "repository": "https://github.com/botpress/studio.git", "author": "Botpress, Inc.", "license": "AGPL-3.0", "scripts": {"build": "yarn clean && yarn workspaces foreach -ptiv run build", "watch": "yarn workspaces foreach -piv run watch", "clean": "yarn workspaces foreach -pi run clean", "start": "ts-node -T --dir scripts start_studio", "setup": "ts-node -T --dir scripts create_default_env --location ./.env.debug", "package": "ts-node -T --dir scripts package", "release": "ts-node -T --dir scripts release", "prettier": "prettier --check './packages/**/*.ts(x)' '!**/*.d.ts'", "eslint": "eslint packages/ --ext .ts,.tsx", "test:unit": "jest -c jest.unit.config.ts"}, "bin": "./packages/studio-be/out/index.js", "pkg": {"scripts": ["./packages/studio-be/out/**/*.js"], "assets": ["./node_modules/@botpress/native-extensions/bin/**/*", "./node_modules/vm2/lib/*.js", "./node_modules/xml-encryption/lib/templates/*.tpl.xml", "./node_modules/npm/**/*", "./packages/studio-be/out/ui/**"], "targets": ["node12-win32-x64", "node12-linux-x64", "node12-macos-x64"]}, "devDependencies": {"@jest/types": "^27.5.0", "@types/jest": "^27.4.0", "@types/node": "^12.18.1", "@typescript-eslint/eslint-plugin": "^5.11.0", "@typescript-eslint/parser": "^5.11.0", "chalk": "^4.1.2", "conventional-changelog": "^3.1.25", "cross-env": "^7.0.3", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsdoc": "37.8.0", "jest": "^27.5.0", "minimist": "^1.2.5", "pkg": "^4.3.7", "prettier": "^2.5.1", "semver": "^7.3.5", "ts-jest": "^27.1.3", "ts-node": "^10.5.0", "typescript": "^4.5.5"}, "workspaces": ["packages/*"], "engines": {"node": "12.18"}, "packageManager": "yarn@3.1.1"}