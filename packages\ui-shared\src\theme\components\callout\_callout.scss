// Copyright 2015 Palantir Technologies, Inc. All rights reserved.
// Licensed under the Apache License, Version 2.0.

@import "../../common/variables";

/*
Callout

Markup:
<div class="#{$ns}-callout {{.modifier}}">
  <h4 class="#{$ns}-heading">Callout Heading</h4>
  It's dangerous to go alone! Take this.
</div>

.#{$ns}-intent-primary - Primary intent
.#{$ns}-intent-success - Success intent
.#{$ns}-intent-warning - Warning intent
.#{$ns}-intent-danger  - Danger intent
.#{$ns}-icon-info-sign - With an icon

Styleguide callout
*/

.#{$ns}-callout {
  @include running-typography();
  position: relative;
  border-radius: $pt-border-radius;
  background-color: rgba($gray3, 0.15);
  width: 100%;
  padding: $pt-grid-size ($pt-grid-size * 1.2) ($pt-grid-size * 0.9);

  // CSS API support
  &[class*="#{$ns}-icon-"] {
    padding-left: ($pt-grid-size * 2) + $pt-icon-size-large;

    &::before {
      @include pt-icon($pt-icon-size-large);
      position: absolute;
      top: $pt-grid-size;
      left: $pt-grid-size;
      color: $pt-icon-color;
    }
  }

  &.#{$ns}-callout-icon {
    padding-left: ($pt-grid-size * 2) + $pt-icon-size-large;

    > .#{$ns}-icon:first-child {
      position: absolute;
      top: $pt-grid-size;
      left: $pt-grid-size;
      color: $pt-icon-color;
    }
  }

  .#{$ns}-heading {
    margin-top: 0;
    margin-bottom: $pt-grid-size / 2;
    line-height: $pt-icon-size-large;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .#{$ns}-dark & {
    background-color: rgba($gray3, 0.2);

    &[class*="#{$ns}-icon-"]::before {
      color: $pt-dark-icon-color;
    }
  }

  @each $intent, $color in $pt-intent-colors {
    &.#{$ns}-intent-#{$intent} {
      background-color: rgba($color, 0.15);

      &[class*="#{$ns}-icon-"]::before,
      > .#{$ns}-icon:first-child,
      .#{$ns}-heading {
        color: map-get($pt-intent-text-colors, $intent);
      }

      .#{$ns}-dark & {
        background-color: rgba($color, 0.25);

        &[class*="#{$ns}-icon-"]::before,
        > .#{$ns}-icon:first-child,
        .#{$ns}-heading {
          color: map-get($pt-dark-intent-text-colors, $intent);
        }
      }
    }
  }

  .#{$ns}-running-text & {
    margin: ($pt-grid-size * 2) 0;
  }
}
