name: Deploy Integrations Staging

on:
  push:
    branches:
      - master

  pull_request:
    paths: 'integrations/**'

  workflow_dispatch:
    inputs:
      force:
        description: 'Force re-deploying integrations'
        type: boolean
        required: false
        default: false

permissions:
  id-token: write
  contents: read

jobs:
  deploy-staging:
    runs-on: depot-ubuntu-22.04-8
    steps:
      - uses: actions/checkout@v2
      - name: Setup
        uses: ./.github/actions/setup
      - name: Deploy Interfaces
        uses: ./.github/actions/deploy-interfaces
        if: ${{ github.event_name != 'pull_request' }}
        with:
          environment: 'staging'
          force: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.force == 'true' }}
          token_cloud_ops_account: ${{ secrets.STAGING_TOKEN_CLOUD_OPS_ACCOUNT }}
          cloud_ops_workspace_id: ${{ secrets.STAGING_CLOUD_OPS_WORKSPACE_ID }}
      - name: Deploy Integrations
        uses: ./.github/actions/deploy-integrations
        with:
          environment: 'staging'
          force: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.force == 'true' }}
          dry_run: ${{ github.event_name == 'pull_request' }}
          sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
          token_cloud_ops_account: ${{ secrets.STAGING_TOKEN_CLOUD_OPS_ACCOUNT }}
          cloud_ops_workspace_id: ${{ secrets.STAGING_CLOUD_OPS_WORKSPACE_ID }}
      - name: Deploy Plugins
        uses: ./.github/actions/deploy-plugins
        if: ${{ github.event_name != 'pull_request' }}
        with:
          environment: 'staging'
          force: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.force == 'true' }}
          token_cloud_ops_account: ${{ secrets.STAGING_TOKEN_CLOUD_OPS_ACCOUNT }}
          cloud_ops_workspace_id: ${{ secrets.STAGING_CLOUD_OPS_WORKSPACE_ID }}
