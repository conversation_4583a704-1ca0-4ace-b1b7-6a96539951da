.content {
  width: 100%;
  height: 100%;
}

.missingTranslation {
  color: red;
}

.tableWrapper {
  padding: 5px;
}

.contentListWrapper {
  height: calc(100% - 36px);

  :global(.ReactTable) {
    height: 100% !important;
  }

  :global(.ReactTable .rt-tbody) {
    overflow: initial !important;
  }
}

.modal {
  .cancel {
    position: absolute;
    left: 90px;
    bottom: 14px;
    height: 34px;
    padding-right: 12px;
    padding-left: 12px;
    min-width: 70px;
  }

  :global(.btn-info) {
    background-color: #46cba0;
    border-color: #46cba0;

    &:hover {
      background-color: #40bd94;
      border-color: #40bd94;
    }
  }
}

.imagePreview {
  height: 16px;
}

.centered {
  text-align: center;
}
