// This file is automatically generated.
// Please do not change this file!
interface CssExports {
  'badge': string;
  'contextMenuLabel': string;
  'error': string;
  'expandBtn': string;
  'fieldError': string;
  'fieldWrapper': string;
  'formHeader': string;
  'formLabel': string;
  'formSelect': string;
  'hasError': string;
  'input': string;
  'items': string;
  'noBorder': string;
  'noPadding': string;
  'noSelect': string;
  'ocean': string;
  'searchBar': string;
  'tag': string;
  'tagInput': string;
  'textarea': string;
  'textareaWrapper': string;
  'typeField': string;
  'warning': string;
  'white': string;
  'wrapper': string;
}
declare var cssExports: CssExports;
export = cssExports;
