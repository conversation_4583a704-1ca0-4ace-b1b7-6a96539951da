{"name": "@botpress/studio-ui", "version": "0.0.1", "author": "Botpress", "license": "AGPL-3.0-only", "scripts": {"build": " yarn run compile-server && node ./webpack.web.js --compile", "build:prod": "cross-env NODE_ENV=production node ./webpack.web.js --compile", "watch": "concurrently -k \"npm run watch-server\" \"node ./webpack.web.js --watch\"", "watch-server": "babel src --source-maps --out-dir public --watch", "compile-server": "babel src --source-maps --out-dir public", "clean": "rimraf public node_modules/.cache"}, "watch": {"compile": {"patterns": ["src"], "extensions": "js,jsx,scss,json,html"}}, "dependencies": {"@analytics/segment": "^1.1.3", "@blueprintjs/core": "^3.23.1", "@blueprintjs/select": "^3.12.0", "@botpress/ui-shared": "*", "analytics": "^0.7.17", "anser": "^1.4.8", "axios": "^0.25.0", "babel-polyfill": "^6.26.0", "bluebird": "^3.7.2", "bluebird-global": "^1.0.1", "bootstrap": "3.4.1", "chalk": "^4.0.0", "classnames": "^2.3.1", "draft-js": "^0.11.7", "draft-js-plugins-editor": "^2.1.1", "draft-js-single-line-plugin": "^2.0.5", "eventemitter2": "^5.0.1", "hash.js": "^1.1.7", "history": "^4.9.0", "howler": "^2.1.2", "joi": "13.7.0", "js-cookie": "^2.2.0", "jsonlint-mod": "^1.7.6", "lodash": "^4.17.21", "moment": "^2.29.1", "ms": "^2.1.3", "mustache": "^2.3.0", "nanoid": "^3.2.0", "npm": "^6.14.8", "progressbar.js": "^1.0.1", "prop-types": "^15.6.0", "query-string": "^5.1.1", "react": "16.8.6", "react-beautiful-dnd": "^13.1.0", "react-bootstrap": "^0.33.1", "react-checkbox-tree": "1.7.2", "react-copy-to-clipboard": "^5.0.2", "react-dom": "16.8.6", "react-ga": "^2.7.0", "react-highlight-words": "^0.16.0", "react-hotkeys": "^1.1.4", "react-icons": "^3.8.0", "react-intl": "^3.12.1", "react-json-tree": "^0.11.2", "react-jsonschema-form": "^1.8.1", "react-loaders": "^3.0.1", "react-markdown": "^4.3.1", "react-redux": "^5.0.6", "react-router": "4.3.1", "react-router-dom": "4.3.1", "react-select": "^5.1.0", "react-split-pane": "^0.1.89", "react-table": "^6.11.5", "reactour": "^1.18.7", "reduce-reducers": "^0.1.2", "redux": "^3.7.2", "redux-actions": "^2.2.1", "redux-thunk": "^2.2.0", "reselect": "^4.0.0", "slate": "0.47.4", "slate-react": "0.22.4", "snarkdown": "^1.2.2", "socket.io-client": "^4.4.1", "storm-react-diagrams": "^5.2.1", "styled-components": "^5.3.0", "typescript": "^4.5.5", "yn": "^4.0.0"}, "devDependencies": {"@types/bluebird-global": "^3.5.13", "@types/classnames": "^2.3.1", "@types/lodash": "^4.14.178", "@types/node": "^12.18.1", "@types/react": "^16.8.6", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-dom": "^16.8.6", "@types/react-jsonschema-form": "^1.7.8", "@types/react-router-dom": "^4.3.3", "@types/reactour": "^1.18.1", "@types/redux-actions": "^2.2.1", "@types/slate": "0.47.1", "@types/slate-react": "0.22.5", "autoprefixer": "^6.5.3", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "^8.0.2", "babel-loader": "^7.1.2", "babel-plugin-root-import": "^5.1.0", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "babel-register": "^6.26.0", "clean-webpack-plugin": "^0.1.19", "concurrently": "^3.5.1", "cross-env": "^7.0.3", "css-loader": "^0.28.11", "css-modules-typescript-loader": "^2.0.3", "decorate-component-with-props": "^1.1.0", "expose-loader": "^0.7.1", "file-loader": "^1.1.11", "filemanager-webpack-plugin": "3.1.1", "find-with-regex": "^1.1.3", "hard-source-webpack-plugin": "git+https://github.com/botpress/hard-source-webpack-plugin.git", "html-webpack-plugin": "^3.2.0", "immutable": "^4.0.0", "node-sass": "^7.0.0", "postcss-loader": "^2.0.8", "raw-loader": "^1.0.0", "rimraf": "^3.0.2", "sass-loader": "^6.0.6", "script-loader": "^0.7.0", "style-loader": "^0.13.1", "terser-webpack-plugin": "^2.2.2", "thread-loader": "^1.1.5", "ts-loader": "^6.0.2", "webpack": "4.46", "webpack-bundle-analyzer": "^3.5.2", "webpack-node-externals": "^1.7.2"}}